# OCR Models (these work together)
easyocr==1.7.2
pytesseract==0.3.13
surya-ocr==0.14.6
protobuf==3.20.3

# PaddleOCR (strict versions, install sep)
# paddleocr==2.7.3
# paddlepaddle-gpu

# Math Expression Models (install sep one by one perhaps?)
# texteller
# texify==0.2.1


# Table Extraction Models (install separately due to conflicts)
# camelot-py
# tabula-py==2.10.0
# pdfplumber==0.11.7

# Text Extraction Models
PyMuPDF==1.26.3
PyPDF2==3.0.1
pdftext
docling

# Other required modules
pydantic
Pillow
rich
opencv-python
huggingface-hub



#texteller requires transformers==4.47 but conflicts with texifyextractor's transformers==4.42.4

#pix2tex which is used  in latexocr/py file, timm==0.5.4 and x-transformer = 0.15.0 
#donut requires timm==0.9.16 conflicting with pix2tex 

#texify(transformers= 4.42.4 and nougat also transformers conflict, need to reinstall everytime to test

#surya ocr transformer conflicts with texteller 
