from omnidocs.tasks.table_extraction.extractors.ppstructure import PPStructureExtractor

def test_ppstructure_table_extraction():
    image_path = "tests/ocr_extraction/assets/invoice.jpg"
    extractor = PPStructureExtractor()
    result = extractor.extract(image_path)
    print("Tables found:", len(getattr(result, "tables", [])))
    if hasattr(result, "tables"):
        for i, table in enumerate(result.tables):
            print(f"Table {i+1}:")
            print(table)
    else:
        print("No tables attribute in result.")

if __name__ == "__main__":
    test_ppstructure_table_extraction()