{"cells": [{"cell_type": "code", "execution_count": 16, "id": "8961066c", "metadata": {}, "outputs": [], "source": ["import sys\n", "import os\n", "import warnings\n", "from PIL import Image, ImageDraw, ImageFont\n", "\n", "# sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))\n", "warnings.filterwarnings(\"ignore\")\n", "os.environ[\"PROTOCOL_BUFFERS_PYTHON_IMPLEMENTATION\"] = \"python\""]}, {"cell_type": "code", "execution_count": 17, "id": "2582a4af", "metadata": {}, "outputs": [], "source": ["def visualize_ocr_boxes(image_path, ocr_result, output_path=\"visualized.png\"):\n", "    \"\"\"\n", "    Visualize OCR boxes using PIL ImageDraw.\n", "    \n", "    Args:\n", "        image_path: Path to the original image\n", "        ocr_result: OCR result object with texts attribute\n", "        output_path: Path to save the annotated image\n", "    \"\"\"\n", "    # Open the original image\n", "    image = Image.open(image_path).convert(\"RGB\")\n", "    \n", "    # Create a copy to draw on\n", "    annotated_image = image.copy()\n", "    draw = ImageDraw.Draw(annotated_image)\n", "    \n", "    # Draw bounding boxes if OCR results exist\n", "    if hasattr(ocr_result, \"texts\") and ocr_result.texts:\n", "        for item in ocr_result.texts:\n", "            bbox = getattr(item, \"bbox\", None)\n", "            if bbox and len(bbox) == 4:\n", "                x1, y1, x2, y2 = bbox\n", "                \n", "                # Draw rectangle around text\n", "                draw.rectangle(\n", "                    [(x1, y1), (x2, y2)], \n", "                    outline='red', \n", "                    width=2\n", "                )\n", "    \n", "    # Save the annotated image\n", "    annotated_image.save(output_path)\n", "    print(f\"Visualization saved to {output_path}\")\n"]}, {"cell_type": "code", "execution_count": 18, "id": "8e62f4ad", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Testing PaddleOCRExtractor\n", "----------------------------------------\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #000080; text-decoration-color: #000080\">INFO    </span> <span style=\"font-weight: bold\">[</span>timestamp<span style=\"font-weight: bold\">]</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">07</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">25</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">12:31:54</span><span style=\"font-weight: bold\">[</span><span style=\"color: #800080; text-decoration-color: #800080\">/</span><span style=\"font-weight: bold\">]</span> | <span style=\"font-weight: bold\">[</span>logger.name<span style=\"font-weight: bold\">]</span>omnidocs.tasks.ocr_extraction.extractors.paddle<span style=\"font-weight: bold\">[</span><span style=\"color: #800080; text-decoration-color: #800080\">/</span><span style=\"font-weight: bold\">]</span> |     \n", "         <span style=\"font-weight: bold\">[</span>function<span style=\"font-weight: bold\">]</span>logging.py:<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">150</span><span style=\"font-weight: bold\">[</span><span style=\"color: #800080; text-decoration-color: #800080\">/</span><span style=\"font-weight: bold\">]</span> | <span style=\"font-weight: bold\">[</span>info<span style=\"font-weight: bold\">]</span>extract completed in <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3.</span>68s<span style=\"font-weight: bold\">[</span><span style=\"color: #800080; text-decoration-color: #800080\">/</span><span style=\"font-weight: bold\">]</span>                                         \n", "</pre>\n"], "text/plain": ["\u001b[34mINFO    \u001b[0m \u001b[1m[\u001b[0mtimestamp\u001b[1m]\u001b[0m\u001b[1;36m2025\u001b[0m-\u001b[1;36m07\u001b[0m-\u001b[1;36m25\u001b[0m \u001b[1;92m12:31:54\u001b[0m\u001b[1m[\u001b[0m\u001b[35m/\u001b[0m\u001b[1m]\u001b[0m | \u001b[1m[\u001b[0mlogger.name\u001b[1m]\u001b[0momnidocs.tasks.ocr_extraction.extractors.paddle\u001b[1m[\u001b[0m\u001b[35m/\u001b[0m\u001b[1m]\u001b[0m |     \n", "         \u001b[1m[\u001b[0mfunction\u001b[1m]\u001b[0mlogging.py:\u001b[1;36m150\u001b[0m\u001b[1m[\u001b[0m\u001b[35m/\u001b[0m\u001b[1m]\u001b[0m | \u001b[1m[\u001b[0minfo\u001b[1m]\u001b[0mextract completed in \u001b[1;36m3.\u001b[0m68s\u001b[1m[\u001b[0m\u001b[35m/\u001b[0m\u001b[1m]\u001b[0m                                         \n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #000080; text-decoration-color: #000080\">INFO    </span> <span style=\"font-weight: bold\">[</span>timestamp<span style=\"font-weight: bold\">]</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">07</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">25</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">12:31:54</span><span style=\"font-weight: bold\">[</span><span style=\"color: #800080; text-decoration-color: #800080\">/</span><span style=\"font-weight: bold\">]</span> | <span style=\"font-weight: bold\">[</span>logger.name<span style=\"font-weight: bold\">]</span>omnidocs.tasks.ocr_extraction.extractors.paddle<span style=\"font-weight: bold\">[</span><span style=\"color: #800080; text-decoration-color: #800080\">/</span><span style=\"font-weight: bold\">]</span> |     \n", "         <span style=\"font-weight: bold\">[</span>function<span style=\"font-weight: bold\">]</span>logging.py:<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">150</span><span style=\"font-weight: bold\">[</span><span style=\"color: #800080; text-decoration-color: #800080\">/</span><span style=\"font-weight: bold\">]</span> | <span style=\"font-weight: bold\">[</span>info<span style=\"font-weight: bold\">]</span>extract completed in <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3.</span>68s<span style=\"font-weight: bold\">[</span><span style=\"color: #800080; text-decoration-color: #800080\">/</span><span style=\"font-weight: bold\">]</span>                                         \n", "</pre>\n"], "text/plain": ["\u001b[34mINFO    \u001b[0m \u001b[1m[\u001b[0mtimestamp\u001b[1m]\u001b[0m\u001b[1;36m2025\u001b[0m-\u001b[1;36m07\u001b[0m-\u001b[1;36m25\u001b[0m \u001b[1;92m12:31:54\u001b[0m\u001b[1m[\u001b[0m\u001b[35m/\u001b[0m\u001b[1m]\u001b[0m | \u001b[1m[\u001b[0mlogger.name\u001b[1m]\u001b[0momnidocs.tasks.ocr_extraction.extractors.paddle\u001b[1m[\u001b[0m\u001b[35m/\u001b[0m\u001b[1m]\u001b[0m |     \n", "         \u001b[1m[\u001b[0mfunction\u001b[1m]\u001b[0mlogging.py:\u001b[1;36m150\u001b[0m\u001b[1m[\u001b[0m\u001b[35m/\u001b[0m\u001b[1m]\u001b[0m | \u001b[1m[\u001b[0minfo\u001b[1m]\u001b[0mextract completed in \u001b[1;36m3.\u001b[0m68s\u001b[1m[\u001b[0m\u001b[35m/\u001b[0m\u001b[1m]\u001b[0m                                         \n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["[2025-07-25 12:31:54,088] [    INFO] logging.py:150 - extract completed in 3.68s\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Text length: 1176 chars\n", "Preview: 'Invoice Account number:PAT20-32 Need help? Normal business hours are: Invoice number6312 Monday-Frid...'\n", "Visualization saved to visualized_PaddleOCRExtractor.png\n", "SUCCESS\n", "\n", "Testing TesseractOCRExtractor\n", "----------------------------------------\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #000080; text-decoration-color: #000080\">INFO    </span> <span style=\"font-weight: bold\">[</span>timestamp<span style=\"font-weight: bold\">]</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">07</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">25</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">12:31:55</span><span style=\"font-weight: bold\">[</span><span style=\"color: #800080; text-decoration-color: #800080\">/</span><span style=\"font-weight: bold\">]</span> | <span style=\"font-weight: bold\">[</span>logger.name<span style=\"font-weight: bold\">]</span>omnidocs.tasks.ocr_extraction.extractors.tesseract_ocr<span style=\"font-weight: bold\">[</span><span style=\"color: #800080; text-decoration-color: #800080\">/</span><span style=\"font-weight: bold\">]</span>\n", "         | <span style=\"font-weight: bold\">[</span>function<span style=\"font-weight: bold\">]</span>logging.py:<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">150</span><span style=\"font-weight: bold\">[</span><span style=\"color: #800080; text-decoration-color: #800080\">/</span><span style=\"font-weight: bold\">]</span> | <span style=\"font-weight: bold\">[</span>info<span style=\"font-weight: bold\">]</span>extract completed in <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1.</span>02s<span style=\"font-weight: bold\">[</span><span style=\"color: #800080; text-decoration-color: #800080\">/</span><span style=\"font-weight: bold\">]</span>                                       \n", "</pre>\n"], "text/plain": ["\u001b[34mINFO    \u001b[0m \u001b[1m[\u001b[0mtimestamp\u001b[1m]\u001b[0m\u001b[1;36m2025\u001b[0m-\u001b[1;36m07\u001b[0m-\u001b[1;36m25\u001b[0m \u001b[1;92m12:31:55\u001b[0m\u001b[1m[\u001b[0m\u001b[35m/\u001b[0m\u001b[1m]\u001b[0m | \u001b[1m[\u001b[0mlogger.name\u001b[1m]\u001b[0momnidocs.tasks.ocr_extraction.extractors.tesseract_ocr\u001b[1m[\u001b[0m\u001b[35m/\u001b[0m\u001b[1m]\u001b[0m\n", "         | \u001b[1m[\u001b[0mfunction\u001b[1m]\u001b[0mlogging.py:\u001b[1;36m150\u001b[0m\u001b[1m[\u001b[0m\u001b[35m/\u001b[0m\u001b[1m]\u001b[0m | \u001b[1m[\u001b[0minfo\u001b[1m]\u001b[0mextract completed in \u001b[1;36m1.\u001b[0m02s\u001b[1m[\u001b[0m\u001b[35m/\u001b[0m\u001b[1m]\u001b[0m                                       \n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #000080; text-decoration-color: #000080\">INFO    </span> <span style=\"font-weight: bold\">[</span>timestamp<span style=\"font-weight: bold\">]</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">07</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">25</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">12:31:55</span><span style=\"font-weight: bold\">[</span><span style=\"color: #800080; text-decoration-color: #800080\">/</span><span style=\"font-weight: bold\">]</span> | <span style=\"font-weight: bold\">[</span>logger.name<span style=\"font-weight: bold\">]</span>omnidocs.tasks.ocr_extraction.extractors.tesseract_ocr<span style=\"font-weight: bold\">[</span><span style=\"color: #800080; text-decoration-color: #800080\">/</span><span style=\"font-weight: bold\">]</span>\n", "         | <span style=\"font-weight: bold\">[</span>function<span style=\"font-weight: bold\">]</span>logging.py:<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">150</span><span style=\"font-weight: bold\">[</span><span style=\"color: #800080; text-decoration-color: #800080\">/</span><span style=\"font-weight: bold\">]</span> | <span style=\"font-weight: bold\">[</span>info<span style=\"font-weight: bold\">]</span>extract completed in <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1.</span>02s<span style=\"font-weight: bold\">[</span><span style=\"color: #800080; text-decoration-color: #800080\">/</span><span style=\"font-weight: bold\">]</span>                                       \n", "</pre>\n"], "text/plain": ["\u001b[34mINFO    \u001b[0m \u001b[1m[\u001b[0mtimestamp\u001b[1m]\u001b[0m\u001b[1;36m2025\u001b[0m-\u001b[1;36m07\u001b[0m-\u001b[1;36m25\u001b[0m \u001b[1;92m12:31:55\u001b[0m\u001b[1m[\u001b[0m\u001b[35m/\u001b[0m\u001b[1m]\u001b[0m | \u001b[1m[\u001b[0mlogger.name\u001b[1m]\u001b[0momnidocs.tasks.ocr_extraction.extractors.tesseract_ocr\u001b[1m[\u001b[0m\u001b[35m/\u001b[0m\u001b[1m]\u001b[0m\n", "         | \u001b[1m[\u001b[0mfunction\u001b[1m]\u001b[0mlogging.py:\u001b[1;36m150\u001b[0m\u001b[1m[\u001b[0m\u001b[35m/\u001b[0m\u001b[1m]\u001b[0m | \u001b[1m[\u001b[0minfo\u001b[1m]\u001b[0mextract completed in \u001b[1;36m1.\u001b[0m02s\u001b[1m[\u001b[0m\u001b[35m/\u001b[0m\u001b[1m]\u001b[0m                                       \n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["[2025-07-25 12:31:55,542] [    INFO] logging.py:150 - extract completed in 1.02s\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Text length: 1095 chars\n", "Preview: '| . ‘Account number PAT20-92 Need help? Normal business hous ore: Invoice number: 6912 i Monday —Fri...'\n", "Visualization saved to visualized_TesseractOCRExtractor.png\n", "SUCCESS\n", "\n", "Testing EasyOCRExtractor\n", "----------------------------------------\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #000080; text-decoration-color: #000080\">INFO    </span> <span style=\"font-weight: bold\">[</span>timestamp<span style=\"font-weight: bold\">]</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">07</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">25</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">12:32:20</span><span style=\"font-weight: bold\">[</span><span style=\"color: #800080; text-decoration-color: #800080\">/</span><span style=\"font-weight: bold\">]</span> | <span style=\"font-weight: bold\">[</span>logger.name<span style=\"font-weight: bold\">]</span>omnidocs.tasks.ocr_extraction.extractors.easy_ocr<span style=\"font-weight: bold\">[</span><span style=\"color: #800080; text-decoration-color: #800080\">/</span><span style=\"font-weight: bold\">]</span> |   \n", "         <span style=\"font-weight: bold\">[</span>function<span style=\"font-weight: bold\">]</span>logging.py:<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">150</span><span style=\"font-weight: bold\">[</span><span style=\"color: #800080; text-decoration-color: #800080\">/</span><span style=\"font-weight: bold\">]</span> | <span style=\"font-weight: bold\">[</span>info<span style=\"font-weight: bold\">]</span>extract completed in <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">21.</span>72s<span style=\"font-weight: bold\">[</span><span style=\"color: #800080; text-decoration-color: #800080\">/</span><span style=\"font-weight: bold\">]</span>                                        \n", "</pre>\n"], "text/plain": ["\u001b[34mINFO    \u001b[0m \u001b[1m[\u001b[0mtimestamp\u001b[1m]\u001b[0m\u001b[1;36m2025\u001b[0m-\u001b[1;36m07\u001b[0m-\u001b[1;36m25\u001b[0m \u001b[1;92m12:32:20\u001b[0m\u001b[1m[\u001b[0m\u001b[35m/\u001b[0m\u001b[1m]\u001b[0m | \u001b[1m[\u001b[0mlogger.name\u001b[1m]\u001b[0momnidocs.tasks.ocr_extraction.extractors.easy_ocr\u001b[1m[\u001b[0m\u001b[35m/\u001b[0m\u001b[1m]\u001b[0m |   \n", "         \u001b[1m[\u001b[0mfunction\u001b[1m]\u001b[0mlogging.py:\u001b[1;36m150\u001b[0m\u001b[1m[\u001b[0m\u001b[35m/\u001b[0m\u001b[1m]\u001b[0m | \u001b[1m[\u001b[0minfo\u001b[1m]\u001b[0mextract completed in \u001b[1;36m21.\u001b[0m72s\u001b[1m[\u001b[0m\u001b[35m/\u001b[0m\u001b[1m]\u001b[0m                                        \n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #000080; text-decoration-color: #000080\">INFO    </span> <span style=\"font-weight: bold\">[</span>timestamp<span style=\"font-weight: bold\">]</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">07</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">25</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">12:32:20</span><span style=\"font-weight: bold\">[</span><span style=\"color: #800080; text-decoration-color: #800080\">/</span><span style=\"font-weight: bold\">]</span> | <span style=\"font-weight: bold\">[</span>logger.name<span style=\"font-weight: bold\">]</span>omnidocs.tasks.ocr_extraction.extractors.easy_ocr<span style=\"font-weight: bold\">[</span><span style=\"color: #800080; text-decoration-color: #800080\">/</span><span style=\"font-weight: bold\">]</span> |   \n", "         <span style=\"font-weight: bold\">[</span>function<span style=\"font-weight: bold\">]</span>logging.py:<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">150</span><span style=\"font-weight: bold\">[</span><span style=\"color: #800080; text-decoration-color: #800080\">/</span><span style=\"font-weight: bold\">]</span> | <span style=\"font-weight: bold\">[</span>info<span style=\"font-weight: bold\">]</span>extract completed in <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">21.</span>72s<span style=\"font-weight: bold\">[</span><span style=\"color: #800080; text-decoration-color: #800080\">/</span><span style=\"font-weight: bold\">]</span>                                        \n", "</pre>\n"], "text/plain": ["\u001b[34mINFO    \u001b[0m \u001b[1m[\u001b[0mtimestamp\u001b[1m]\u001b[0m\u001b[1;36m2025\u001b[0m-\u001b[1;36m07\u001b[0m-\u001b[1;36m25\u001b[0m \u001b[1;92m12:32:20\u001b[0m\u001b[1m[\u001b[0m\u001b[35m/\u001b[0m\u001b[1m]\u001b[0m | \u001b[1m[\u001b[0mlogger.name\u001b[1m]\u001b[0momnidocs.tasks.ocr_extraction.extractors.easy_ocr\u001b[1m[\u001b[0m\u001b[35m/\u001b[0m\u001b[1m]\u001b[0m |   \n", "         \u001b[1m[\u001b[0mfunction\u001b[1m]\u001b[0mlogging.py:\u001b[1;36m150\u001b[0m\u001b[1m[\u001b[0m\u001b[35m/\u001b[0m\u001b[1m]\u001b[0m | \u001b[1m[\u001b[0minfo\u001b[1m]\u001b[0mextract completed in \u001b[1;36m21.\u001b[0m72s\u001b[1m[\u001b[0m\u001b[35m/\u001b[0m\u001b[1m]\u001b[0m                                        \n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["[2025-07-25 12:32:20,590] [    INFO] logging.py:150 - extract completed in 21.72s\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Text length: 1124 chars\n", "Preview: 'Invoice Account number: PATZD 32 Need holp? Normal business hours are: Involce number: 6312 Monday -...'\n", "Visualization saved to visualized_EasyOCRExtractor.png\n", "SUCCESS\n"]}], "source": ["def test_ocr_extraction():\n", "    from omnidocs.tasks.ocr_extraction.extractors.paddle import PaddleOCRExtractor\n", "    from omnidocs.tasks.ocr_extraction.extractors.tesseract_ocr import TesseractOCRExtractor\n", "    from omnidocs.tasks.ocr_extraction.extractors.easy_ocr import EasyOCRExtractor\n", "\n", "    extractors = [\n", "        PaddleOCRExtractor,\n", "        TesseractOCRExtractor,\n", "        EasyOCRExtractor,\n", "    ]\n", "\n", "    image_path = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\CogLab\\\\11-07-2025\\\\Omnidocs\\\\tests\\\\ocr_extraction\\\\assets\\\\invoice.jpg\"\n", "    \n", "    for extractor_cls in extractors:\n", "        print(f\"\\nTesting {extractor_cls.__name__}\")\n", "        print(\"-\" * 40)\n", "        \n", "        try:\n", "            result = extractor_cls().extract(image_path)\n", "            print(f\"Text length: {len(result.full_text)} chars\")\n", "            print(f\"Preview: '{result.full_text[:100]}...'\")\n", "            \n", "            vis_path = f\"visualized_{extractor_cls.__name__}.png\"\n", "            visualize_ocr_boxes(image_path, result, vis_path)\n", "            \n", "            print(\"SUCCESS\")\n", "        except Exception as e:\n", "            print(f\"ERROR: {e}\")\n", "\n", "\n", "if __name__ == \"__main__\":\n", "    test_ocr_extraction()\n"]}, {"cell_type": "code", "execution_count": null, "id": "34fe8fee", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "final", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 5}