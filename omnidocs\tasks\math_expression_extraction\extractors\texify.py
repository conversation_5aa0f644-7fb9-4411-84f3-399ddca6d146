import sys
import logging
from typing import Union, List, Dict, Any, Optional, Tuple
from pathlib import Path
import cv2
import numpy as np
from PIL import Image
from omnidocs.utils.logging import get_logger, log_execution_time
from omnidocs.tasks.math_expression_extraction.base import BaseLatexExtractor, BaseLatexMapper, LatexOutput

logger = get_logger(__name__)

class TexifyMapper(BaseLatexMapper):
    """Label mapper for Texify model output."""
    
    def _setup_mapping(self):
        # Add any necessary mappings between Texify's LaTeX format and standard format
        mapping = {
            r"\begin{equation}": "",  # Remove equation environment
            r"\end{equation}": "",
        }
        self._mapping = mapping
        self._reverse_mapping = {v: k for k, v in mapping.items()}

class TexifyExtractor(BaseLatexExtractor):
    """Texify-based LaTeX expression extraction implementation."""
    
    def __init__(
        self,
        device: Optional[str] = None,
        show_log: bool = False,
        **kwargs
    ):
        """Initialize Texify Extractor."""
        super().__init__(device=device, show_log=show_log)
        
        self._label_mapper = TexifyMapper()
        
        try:
            from texify.inference import batch_inference
            from texify.model.model import load_model
            from texify.model.processor import load_processor
        except ImportError as e:
            logger.error("Failed to import texify")
            raise ImportError(
                "texify is not available. Please install it with: pip install texify"
            ) from e
            
        try:
            self.model = load_model()
            self.processor = load_processor()
            if self.device == "cuda":
                self.model = self.model.cuda()
                
            if self.show_log:
                logger.success("Model initialized successfully")
        except Exception as e:
            logger.error("Failed to initialize model", exc_info=True)
            raise
    
    def _download_model(self) -> Path:
        """Model download handled by texify library."""
        logger.info("Model downloading handled by texify library")
        return None
    
    def _load_model(self) -> None:
        """Model loaded in __init__."""
        pass
    
    @log_execution_time
    def extract(
        self,
        input_path: Union[str, Path, Image.Image],
        **kwargs
    ) -> LatexOutput:
        """Extract LaTeX expressions using Texify."""
        try:
            from texify.inference import batch_inference
            
            # Preprocess input
            images = self.preprocess_input(input_path)
            
            # Run inference
            results = batch_inference(images, self.model, self.processor)
            
            # Process results
            expressions = []
            confidences = []
            
            for result in results:
                # Handle both string and dict results
                if isinstance(result, str):
                    latex_expr = result
                    confidence = 1.0  # Default confidence for string results
                else:
                    latex_expr = result.get('latex', '')
                    confidence = result.get('confidence', 1.0)

                # Map to standard format
                mapped_expr = self.map_expression(latex_expr)

                expressions.append(mapped_expr)
                confidences.append(confidence)
            
            return LatexOutput(
                expressions=expressions,
                confidences=confidences,
                source_img_size=images[0].size if images else None
            )
            
        except Exception as e:
            logger.error("Error during extraction", exc_info=True)
            raise