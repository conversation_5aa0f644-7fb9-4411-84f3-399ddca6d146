model:
  arch: unimernet
  model_type: unimernet
  model_config:
    model_name: ./models/unimernet_base
    max_seq_len: 1536
  load_pretrained: True
  pretrained: './models/unimernet_base/pytorch_model.pth'
  tokenizer_config:
    path: ./models/unimernet_base

datasets:
  formula_rec_eval:
    vis_processor:
      eval:
        name: "formula_image_eval"
        image_size:
          - 192
          - 672

run:
  task: base_task
  device: "cuda"
  generate_cfg:
    temperature: 0.0