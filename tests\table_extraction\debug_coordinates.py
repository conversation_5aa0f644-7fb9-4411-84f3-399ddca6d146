#!/usr/bin/env python3

import sys
sys.path.insert(0, '../..')

from omnidocs.tasks.table_extraction.extractors import CamelotExtractor, PDFPlumberExtractor
from pathlib import Path

def debug_camelot():
    print("=== DEBUGGING CAMELOT ===")
    
    extractor = CamelotExtractor(show_log=True)
    pdf_path = Path('assets/table_document.pdf')
    
    try:
        # Extract tables
        result = extractor.extract(pdf_path)
        print(f"Extracted {len(result.tables)} tables")
        print(f"Image size: {result.source_img_size}")
        
        if result.tables:
            table = result.tables[0]
            print(f"Table bbox: {table.bbox}")
            print(f"Table dimensions: {table.num_rows}x{table.num_cols}")
            
            # Show first few cells
            print("First 5 cell bboxes:")
            for i, cell in enumerate(table.cells[:5]):
                print(f"  Cell {i} ({cell.row},{cell.col}): {cell.bbox} - '{cell.text}'")
                
            # Check if coordinates make sense
            img_w, img_h = result.source_img_size
            x1, y1, x2, y2 = table.bbox
            
            print(f"Table bbox check:")
            print(f"  x: {x1} to {x2} (image width: {img_w})")
            print(f"  y: {y1} to {y2} (image height: {img_h})")
            
            if 0 <= x1 < x2 <= img_w and 0 <= y1 < y2 <= img_h:
                print("  ✓ Table bbox is within image bounds")
            else:
                print("  ✗ Table bbox is outside image bounds")
                
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

def debug_pdfplumber():
    print("\n=== DEBUGGING PDFPLUMBER ===")
    
    extractor = PDFPlumberExtractor(show_log=True)
    pdf_path = Path('assets/table_document.pdf')
    
    try:
        # Extract tables
        result = extractor.extract(pdf_path)
        print(f"Extracted {len(result.tables)} tables")
        print(f"Image size: {result.source_img_size}")
        
        if result.tables:
            table = result.tables[0]
            print(f"Table bbox: {table.bbox}")
            print(f"Table dimensions: {table.num_rows}x{table.num_cols}")
            
            # Show first few cells
            print("First 5 cell bboxes:")
            for i, cell in enumerate(table.cells[:5]):
                print(f"  Cell {i} ({cell.row},{cell.col}): {cell.bbox} - '{cell.text}'")
                
            # Check if coordinates make sense
            img_w, img_h = result.source_img_size
            x1, y1, x2, y2 = table.bbox
            
            print(f"Table bbox check:")
            print(f"  x: {x1} to {x2} (image width: {img_w})")
            print(f"  y: {y1} to {y2} (image height: {img_h})")
            
            if 0 <= x1 < x2 <= img_w and 0 <= y1 < y2 <= img_h:
                print("  ✓ Table bbox is within image bounds")
            else:
                print("  ✗ Table bbox is outside image bounds")
                
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_camelot()
    debug_pdfplumber()
