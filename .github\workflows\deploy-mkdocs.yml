name: Deploy MkDocs to GitHub Pages

on:
  push:
    branches:
      - main
    paths:
      - 'docs/**'
      - 'mkdocs.yml'
      - '.github/workflows/deploy-docs.yml'
  workflow_dispatch:  # Allows manual trigger from GitHub UI

permissions:
  contents: write

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - uses: actions/setup-python@v4
        with:
          python-version: 3.x
          
      - uses: actions/cache@v2
        with:
          key: ${{ github.ref }}
          path: .cache
          
      - name: Install dependencies
        run: |
          pip install mkdocs-material
          pip install mkdocs-jupyter
          pip install mkdocs-exclude
          pip install "mkdocstrings[python]"
          pip install mkdocs-material[imaging]
          
      - name: Deploy documentation
        run: mkdocs gh-deploy --force