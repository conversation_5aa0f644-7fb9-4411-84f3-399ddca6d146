import time
import numpy as np
from typing import Union, List, Dict, Any, Optional, Tuple
from pathlib import Path
from PIL import Image
import cv2

from omnidocs.utils.logging import get_logger, log_execution_time
from omnidocs.tasks.table_extraction.base import BaseTableExtractor, BaseTableMapper, TableOutput, Table, TableCell

logger = get_logger(__name__)


class CamelotMapper(BaseTableMapper):
    """Label mapper for Camelot table extraction output."""
    
    def __init__(self):
        super().__init__('camelot')
        self._setup_mapping()
    
    def _setup_mapping(self):
        """Setup extraction method mappings for Camelot."""
        self._methods = {
            'lattice': 'lattice',  # For tables with clear borders
            'stream': 'stream',    # For tables without clear borders
        }
        self._default_method = 'lattice'


class CamelotExtractor(BaseTableExtractor):
    """Camelot based table extraction implementation."""
    
    def __init__(
        self,
        device: Optional[str] = None,
        show_log: bool = False,
        method: str = 'lattice',
        pages: str = '1',
        flavor: str = 'lattice',
        **kwargs
    ):
        """Initialize Camelot Table Extractor."""
        super().__init__(
            device=device,
            show_log=show_log,
            engine_name='camelot'
        )
        
        self._label_mapper = CamelotMapper()
        self.method = method
        self.pages = pages
        self.flavor = flavor
        
        try:
            import camelot
            self.camelot = camelot
            
        except ImportError as e:
            logger.error("Failed to import Camelot")
            raise ImportError(
                "Camelot is not available. Please install it with: pip install camelot-py[cv]"
            ) from e
        
        self._load_model()
    
    def _download_model(self) -> Optional[Path]:
        """Camelot doesn't require model download, it's rule-based."""
        if self.show_log:
            logger.info("Camelot is rule-based and doesn't require model download")
        return None
    
    def _load_model(self) -> None:
        """Load Camelot (no actual model loading needed)."""
        try:
            if self.show_log:
                logger.info("Camelot extractor initialized")
        except Exception as e:
            logger.error("Failed to initialize Camelot extractor", exc_info=True)
            raise
    
    def _get_pdf_page_info(self, pdf_path: Union[str, Path], page_num: int = 0) -> Tuple[float, float]:
        """Get PDF page dimensions in points."""
        try:
            import fitz  # PyMuPDF
            doc = fitz.open(str(pdf_path))
            page = doc[page_num]
            width = float(page.rect.width)
            height = float(page.rect.height)
            doc.close()
            return width, height
        except Exception as e:
            if self.show_log:
                logger.warning(f"Could not get PDF dimensions: {e}")
            # Return standard letter size in points
            return 612.0, 792.0
    
    def _convert_pdf_to_image(self, pdf_path: Union[str, Path], dpi: int = 200) -> Tuple[Image.Image, float]:
        """Convert PDF first page to image and return scaling factor."""
        try:
            from pdf2image import convert_from_path
            images = convert_from_path(str(pdf_path), dpi=dpi, first_page=1, last_page=1)
            
            if images:
                image = images[0]
                # Calculate scaling factor from PDF points to image pixels
                pdf_width, pdf_height = self._get_pdf_page_info(pdf_path)
                scale_x = image.width / pdf_width
                scale_y = image.height / pdf_height
                
                return image, (scale_x + scale_y) / 2  # Average scaling
            else:
                raise Exception("No images converted")
                
        except ImportError:
            logger.error("pdf2image not available. Install with: pip install pdf2image")
            raise
        except Exception as e:
            logger.error(f"Error converting PDF to image: {e}")
            raise

    def _normalize_bbox(self, bbox: List[float], pdf_size: Tuple[float, float], img_size: Tuple[int, int]) -> List[float]:
        """
        Convert Camelot bbox from PDF coordinates to image coordinates.
        
        Args:
            bbox: [x0, y0, x1, y1] in PDF points (bottom-left origin)
            pdf_size: (pdf_width_pts, pdf_height_pts)
            img_size: (img_width_px, img_height_px)
            
        Returns:
            [x0, y0, x1, y1] in image pixels (top-left origin)
        """
        if not bbox or len(bbox) < 4:
            return [0.0, 0.0, 100.0, 100.0]

        pdf_w, pdf_h = pdf_size
        img_w, img_h = img_size
        x0_pt, y0_pt, x1_pt, y1_pt = bbox

        # Scale from PDF points to image pixels
        scale_x = img_w / pdf_w
        scale_y = img_h / pdf_h
        
        x0_px = x0_pt * scale_x
        x1_px = x1_pt * scale_x
        
        # Convert Y coordinates: PDF origin is bottom-left, image origin is top-left
        y0_px = img_h - (y1_pt * scale_y)  # PDF y1 becomes image y0
        y1_px = img_h - (y0_pt * scale_y)  # PDF y0 becomes image y1

        # Ensure proper ordering and clamp to image bounds
        x0_px = max(0.0, min(x0_px, float(img_w)))
        x1_px = max(0.0, min(x1_px, float(img_w)))
        y0_px = max(0.0, min(y0_px, float(img_h)))
        y1_px = max(0.0, min(y1_px, float(img_h)))
        
        # Ensure x1 >= x0 and y1 >= y0
        if x1_px < x0_px:
            x0_px, x1_px = x1_px, x0_px
        if y1_px < y0_px:
            y0_px, y1_px = y1_px, y0_px

        return [x0_px, y0_px, x1_px, y1_px]

    def _get_table_bbox_from_camelot(self, camelot_table, pdf_size: Tuple[float, float], img_size: Tuple[int, int]) -> List[float]:
        """Extract and convert table bounding box from Camelot table object."""
        try:
            bbox = None

            # Try different attributes where Camelot might store bbox
            for attr in ['_bbox', 'bbox']:
                if hasattr(camelot_table, attr):
                    bbox_val = getattr(camelot_table, attr)
                    if bbox_val is not None:
                        bbox = bbox_val
                        break

            # Try parsing report
            if bbox is None and hasattr(camelot_table, 'parsing_report'):
                bbox = camelot_table.parsing_report.get('bbox')

            if bbox is not None:
                return self._normalize_bbox(bbox, pdf_size, img_size)

            # Fallback: calculate from cells
            if hasattr(camelot_table, 'cells') and camelot_table.cells:
                return self._calculate_table_bbox_from_cells(camelot_table.cells, pdf_size, img_size)

            # Final fallback: use table data to estimate
            if hasattr(camelot_table, 'df'):
                # Rough estimation based on typical table placement
                pdf_w, pdf_h = pdf_size
                estimated_bbox = [50, pdf_h - 300, pdf_w - 50, pdf_h - 50]  # [x1, y1, x2, y2] in PDF points
                return self._normalize_bbox(estimated_bbox, pdf_size, img_size)

            return [0.0, 0.0, 100.0, 100.0]
            
        except Exception as e:
            if self.show_log:
                logger.warning(f"Could not get table bbox: {e}")
            return [0.0, 0.0, 100.0, 100.0]

    def _calculate_table_bbox_from_cells(self, cells, pdf_size: Tuple[float, float], img_size: Tuple[int, int]) -> List[float]:
        """Calculate table bbox from cell bboxes."""
        try:
            all_bboxes = []
            
            # Flatten cells if it's a 2D array
            flat_cells = []
            if isinstance(cells, list) and cells:
                if isinstance(cells[0], list):
                    # 2D array
                    for row in cells:
                        flat_cells.extend(row)
                else:
                    # Already flat
                    flat_cells = cells
            
            for cell in flat_cells:
                cell_bbox = self._get_cell_bbox_raw(cell)
                if cell_bbox:
                    normalized = self._normalize_bbox(cell_bbox, pdf_size, img_size)
                    all_bboxes.append(normalized)
            
            if all_bboxes:
                # Calculate encompassing bbox
                min_x = min(bbox[0] for bbox in all_bboxes)
                min_y = min(bbox[1] for bbox in all_bboxes)
                max_x = max(bbox[2] for bbox in all_bboxes)
                max_y = max(bbox[3] for bbox in all_bboxes)
                
                return [min_x, min_y, max_x, max_y]
            
            return [0.0, 0.0, 100.0, 100.0]
            
        except Exception as e:
            if self.show_log:
                logger.debug(f"Could not calculate table bbox from cells: {e}")
            return [0.0, 0.0, 100.0, 100.0]

    def _get_cell_bbox_raw(self, cell_obj) -> Optional[List[float]]:
        """Get raw bbox from a cell object without coordinate conversion."""
        try:
            # Try different ways Camelot might store cell bbox
            for attr in ['bbox', '_bbox']:
                if hasattr(cell_obj, attr):
                    bbox = getattr(cell_obj, attr)
                    if bbox is not None:
                        return list(bbox)
            
            # Try individual coordinates
            if all(hasattr(cell_obj, attr) for attr in ['x1', 'y1', 'x2', 'y2']):
                return [cell_obj.x1, cell_obj.y1, cell_obj.x2, cell_obj.y2]
            
            return None
            
        except Exception:
            return None

    def _get_cell_bbox_from_camelot(self, camelot_table, row_idx: int, col_idx: int, 
                                   pdf_size: Tuple[float, float], img_size: Tuple[int, int]) -> List[float]:
        """Get cell bbox with proper coordinate conversion."""
        try:
            if hasattr(camelot_table, 'cells') and camelot_table.cells:
                if (row_idx < len(camelot_table.cells) and 
                    col_idx < len(camelot_table.cells[row_idx])):
                    
                    cell = camelot_table.cells[row_idx][col_idx]
                    raw_bbox = self._get_cell_bbox_raw(cell)
                    
                    if raw_bbox:
                        return self._normalize_bbox(raw_bbox, pdf_size, img_size)
            
            # Fallback to estimation
            return [0.0, 0.0, 50.0, 20.0]
            
        except Exception as e:
            if self.show_log:
                logger.debug(f"Could not get cell bbox for ({row_idx}, {col_idx}): {e}")
            return [0.0, 0.0, 50.0, 20.0]

    def _estimate_cell_bbox(self, table_bbox: List[float], row: int, col: int,
                           num_rows: int, num_cols: int) -> List[float]:
        """Estimate cell bounding box based on table bbox and grid position.

        TODO: Cell bbox estimation is still broken for PDF extractors.
        Current issues:
        - Grid-based estimation doesn't account for actual cell sizes
        - Coordinate transformation accuracy issues
        - Need better cell detection from Camelot's internal data
        """
        if not table_bbox or len(table_bbox) < 4:
            return [0.0, 0.0, 50.0, 20.0]

        x1, y1, x2, y2 = table_bbox

        # Calculate cell dimensions
        cell_width = (x2 - x1) / max(num_cols, 1)
        cell_height = (y2 - y1) / max(num_rows, 1)

        # Calculate cell position
        cell_x1 = x1 + (col * cell_width)
        cell_y1 = y1 + (row * cell_height)
        cell_x2 = cell_x1 + cell_width
        cell_y2 = cell_y1 + cell_height

        return [cell_x1, cell_y1, cell_x2, cell_y2]

    def _clamp_bbox_to_image(self, bbox: List[float], img_width: int, img_height: int) -> List[float]:
        """Clamp bounding box to image boundaries."""
        if not bbox or len(bbox) < 4:
            return bbox
            
        x1, y1, x2, y2 = bbox
        
        x1 = max(0.0, min(x1, float(img_width - 1)))
        y1 = max(0.0, min(y1, float(img_height - 1)))
        x2 = max(x1 + 1.0, min(x2, float(img_width)))
        y2 = max(y1 + 1.0, min(y2, float(img_height)))
        
        return [x1, y1, x2, y2]

    def postprocess_output(self, raw_output: Any, image: Image.Image, 
                          pdf_path: Union[str, Path]) -> TableOutput:
        """Convert Camelot output with proper coordinate handling."""
        tables = []
        img_width, img_height = image.size
        
        # Get PDF dimensions
        pdf_width, pdf_height = self._get_pdf_page_info(pdf_path)
        pdf_size = (pdf_width, pdf_height)
        img_size = (img_width, img_height)

        if self.show_log:
            logger.info(f"PDF size: {pdf_width}x{pdf_height}, Image size: {img_width}x{img_height}")

        for i, camelot_table in enumerate(raw_output):
            df = camelot_table.df
            num_rows, num_cols = df.shape
            
            if self.show_log:
                logger.info(f"Processing table {i}: {num_rows}x{num_cols}")
            
            # Get table bbox (converted to image coordinates)
            table_bbox = self._get_table_bbox_from_camelot(camelot_table, pdf_size, img_size)
            
            # Clamp to image bounds
            table_bbox = self._clamp_bbox_to_image(table_bbox, img_width, img_height)

            # Process cells
            cells = []
            for row_idx in range(num_rows):
                for col_idx in range(num_cols):
                    cell_text = str(df.iloc[row_idx, col_idx]).strip()
                    
                    # Get cell bbox (converted to image coordinates)
                    cell_bbox = self._get_cell_bbox_from_camelot(
                        camelot_table, row_idx, col_idx, pdf_size, img_size
                    )
                    
                    # If no cell bbox available, estimate from table grid
                    if cell_bbox == [0.0, 0.0, 50.0, 20.0]:  # Default fallback
                        cell_bbox = self._estimate_cell_bbox(
                            table_bbox, row_idx, col_idx, num_rows, num_cols
                        )

                    # Clamp to image bounds
                    cell_bbox = self._clamp_bbox_to_image(cell_bbox, img_width, img_height)

                    cell = TableCell(
                        text=cell_text,
                        row=row_idx,
                        col=col_idx,
                        rowspan=1,
                        colspan=1,
                        bbox=cell_bbox,
                        confidence=camelot_table.accuracy / 100.0,
                        is_header=(row_idx == 0)
                    )
                    cells.append(cell)

            # Create table
            table = Table(
                cells=cells,
                num_rows=num_rows,
                num_cols=num_cols,
                bbox=table_bbox,
                confidence=camelot_table.accuracy / 100.0,
                table_id=f"table_{i}",
                structure_confidence=camelot_table.accuracy / 100.0
            )
            tables.append(table)
            
            if self.show_log:
                logger.info(f"Table {i} final bbox: {table_bbox}")

        return TableOutput(
            tables=tables,
            source_img_size=(img_width, img_height),
            metadata={
                'engine': 'camelot',
                'method': self.method,
                'flavor': self.flavor,
                'pdf_size': (pdf_width, pdf_height)
            }
        )
    
    @log_execution_time
    def extract(
        self,
        input_path: Union[str, Path, Image.Image],
        **kwargs
    ) -> TableOutput:
        """Extract tables using Camelot."""
        try:
            # Camelot works with PDF files only
            if isinstance(input_path, (str, Path)):
                pdf_path = Path(input_path)
                if pdf_path.suffix.lower() != '.pdf':
                    raise ValueError("Camelot only works with PDF files")
                
                # Extract tables from PDF
                tables = self.camelot.read_pdf(
                    str(pdf_path),
                    pages=self.pages,
                    flavor=self.flavor,
                    **kwargs
                )
                
                if self.show_log:
                    logger.info(f"Found {len(tables)} tables in PDF")
                
                # Convert PDF to image for coordinate scaling
                image, scale_factor = self._convert_pdf_to_image(pdf_path)
                
            else:
                raise ValueError("Camelot requires PDF file path, not image data")

            # Convert to standardized format
            result = self.postprocess_output(tables, image, pdf_path)
            
            if self.show_log:
                logger.info(f"Extracted {len(result.tables)} tables using Camelot")
                for i, table in enumerate(result.tables):
                    logger.info(f"Table {i}: bbox={table.bbox}, cells={len(table.cells)}")
            
            return result
            
        except Exception as e:
            logger.error("Error during Camelot extraction", exc_info=True)
            return TableOutput(
                tables=[],
                source_img_size=None,
                processing_time=None,
                metadata={"error": str(e)}
            )
    
    def predict(self, pdf_path: Union[str, Path], **kwargs):
        """Predict method for compatibility with original interface."""
        try:
            result = self.extract(pdf_path, **kwargs)
            
            # Convert to original format
            table_res = []
            for table in result.tables:
                table_data = {
                    "table_id": table.table_id,
                    "bbox": table.bbox,
                    "confidence": table.confidence,
                    "cells": [cell.to_dict() for cell in table.cells],
                    "num_rows": table.num_rows,
                    "num_cols": table.num_cols
                }
                table_res.append(table_data)
            
            return table_res
            
        except Exception as e:
            logger.error("Error during Camelot prediction", exc_info=True)
            return []
