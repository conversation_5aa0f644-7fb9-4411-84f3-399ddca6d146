import sys
import os
import warnings
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from PIL import Image


sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))
warnings.filterwarnings("ignore")
os.environ["PROTOCOL_BUFFERS_PYTHON_IMPLEMENTATION"] = "python"



def visualize_ocr_boxes(image_path, ocr_result, output_path="visualized.png"):
    image = Image.open(image_path)
    fig, ax = plt.subplots(1)
    ax.imshow(image)

    if hasattr(ocr_result, "texts") and ocr_result.texts:
        for item in ocr_result.texts:
            bbox = getattr(item, "bbox", None)
            if bbox and len(bbox) == 4:
                x1, y1, x2, y2 = bbox
                rect = patches.Rectangle(
                    (x1, y1), x2 - x1, y2 - y1,
                    linewidth=2, edgecolor='r', facecolor='none'
                )
                ax.add_patch(rect)

    plt.axis('off')
    plt.savefig(output_path, bbox_inches='tight', pad_inches=0)
    plt.close(fig)
    print(f"Visualization saved to {output_path}")


def test_ocr_extraction():
    from omnidocs.tasks.ocr_extraction.extractors.paddle import PaddleOCRExtractor
    from omnidocs.tasks.ocr_extraction.extractors.tesseract_ocr import TesseractOCRExtractor
    from omnidocs.tasks.ocr_extraction.extractors.easy_ocr import EasyOCRExtractor
    from omnidocs.tasks.ocr_extraction.extractors.surya_ocr import SuryaOCRExtractor

    extractors = [
        PaddleOCRExtractor,
        TesseractOCRExtractor,
        EasyOCRExtractor,
        SuryaOCRExtractor,
    ]

    image_path = "tests/ocr_extraction/assets/invoice.jpg"

    for extractor_cls in extractors:
        print(f"\nTesting {extractor_cls.__name__}")
        print("-" * 40)
        
        try:
            result = extractor_cls().extract(image_path)
            print(f"Text length: {len(result.full_text)} chars")
            print(f"Preview: '{result.full_text[:100]}...'")
            
            vis_path = f"visualized_{extractor_cls.__name__}.png"
            visualize_ocr_boxes(image_path, result, vis_path)
            
            print("SUCCESS")
        except Exception as e:
            print(f"ERROR: {e}")

if __name__ == "__main__":
    test_ocr_extraction()