{"cells": [{"cell_type": "code", "execution_count": 1, "id": "8b6f615e", "metadata": {}, "outputs": [], "source": ["from omnidocs.tasks.ocr_extraction.extractors.paddle import PaddleOCRExtractor\n", "from omnidocs.tasks.ocr_extraction.extractors.tesseract_ocr import TesseractOCRExtractor\n", "from omnidocs.tasks.ocr_extraction.extractors.easy_ocr import EasyOCRExtractor\n", "from omnidocs.tasks.ocr_extraction.extractors.surya_ocr import SuryaOCRExtractor"]}, {"cell_type": "code", "execution_count": 2, "id": "3c009f75", "metadata": {}, "outputs": [], "source": ["def test_ocr_extraction():\n", "    from omnidocs.tasks.ocr_extraction.extractors.paddle import PaddleOCRExtractor\n", "    from omnidocs.tasks.ocr_extraction.extractors.tesseract_ocr import TesseractOCRExtractor\n", "    from omnidocs.tasks.ocr_extraction.extractors.easy_ocr import EasyOCRExtractor\n", "    from omnidocs.tasks.ocr_extraction.extractors.surya_ocr import SuryaOCRExtractor\n", "    \n", "    extractors = [PaddleOCRExtractor, TesseractOCRExtractor, EasyOCRExtractor, SuryaOCRExtractor]\n", "    image_path = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\CogLab\\\\11-07-2025\\\\Omnidocs\\\\tests\\\\ocr_extraction\\\\assets\\\\invoice.jpg\"\n", "    \n", "    for extractor_cls in extractors:\n", "        print(f\"\\nTesting {extractor_cls.__name__}\")\n", "        print(\"-\" * 40)\n", "        \n", "        try:\n", "            extractor = extractor_cls()\n", "            result = extractor.extract(image_path)\n", "            print(f\"Text length: {len(result.full_text)} chars\")\n", "        \n", "            vis_path = f\"visualized_{extractor_cls.__name__}.png\"\n", "            extractor.visualize(result, image_path, vis_path)\n", "\n", "\n", "            #load and visualize, if already saved as json\n", "            #extractor.visualize_from_json(\"image.jpg\", \"results.json\", \"viz.png\")\n", "\n", "\n", "            # with custom styling\n", "            extractor.visualize(\n", "                result, \n", "                image_path, \n", "                f\"styled_{extractor_cls.__name__}.png\",\n", "                box_color='green',\n", "                box_width=3,\n", "                show_text=True,\n", "                text_color='red'\n", "            )\n", "            \n", "            print(\"SUCCESS\")\n", "        except Exception as e:\n", "            print(f\"ERROR: {e}\")"]}, {"cell_type": "code", "execution_count": 3, "id": "2a1bc4dc", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Testing PaddleOCRExtractor\n", "----------------------------------------\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #000080; text-decoration-color: #000080\">INFO    </span> <span style=\"font-weight: bold\">[</span>timestamp<span style=\"font-weight: bold\">]</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">07</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">26</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">09:03:33</span><span style=\"font-weight: bold\">[</span><span style=\"color: #800080; text-decoration-color: #800080\">/</span><span style=\"font-weight: bold\">]</span> | <span style=\"font-weight: bold\">[</span>logger.name<span style=\"font-weight: bold\">]</span>omnidocs.tasks.ocr_extraction.extractors.paddle<span style=\"font-weight: bold\">[</span><span style=\"color: #800080; text-decoration-color: #800080\">/</span><span style=\"font-weight: bold\">]</span> |     \n", "         <span style=\"font-weight: bold\">[</span>function<span style=\"font-weight: bold\">]</span>logging.py:<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">150</span><span style=\"font-weight: bold\">[</span><span style=\"color: #800080; text-decoration-color: #800080\">/</span><span style=\"font-weight: bold\">]</span> | <span style=\"font-weight: bold\">[</span>info<span style=\"font-weight: bold\">]</span>extract completed in <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">7.</span>61s<span style=\"font-weight: bold\">[</span><span style=\"color: #800080; text-decoration-color: #800080\">/</span><span style=\"font-weight: bold\">]</span>                                         \n", "</pre>\n"], "text/plain": ["\u001b[34mINFO    \u001b[0m \u001b[1m[\u001b[0mtimestamp\u001b[1m]\u001b[0m\u001b[1;36m2025\u001b[0m-\u001b[1;36m07\u001b[0m-\u001b[1;36m26\u001b[0m \u001b[1;92m09:03:33\u001b[0m\u001b[1m[\u001b[0m\u001b[35m/\u001b[0m\u001b[1m]\u001b[0m | \u001b[1m[\u001b[0mlogger.name\u001b[1m]\u001b[0momnidocs.tasks.ocr_extraction.extractors.paddle\u001b[1m[\u001b[0m\u001b[35m/\u001b[0m\u001b[1m]\u001b[0m |     \n", "         \u001b[1m[\u001b[0mfunction\u001b[1m]\u001b[0mlogging.py:\u001b[1;36m150\u001b[0m\u001b[1m[\u001b[0m\u001b[35m/\u001b[0m\u001b[1m]\u001b[0m | \u001b[1m[\u001b[0minfo\u001b[1m]\u001b[0mextract completed in \u001b[1;36m7.\u001b[0m61s\u001b[1m[\u001b[0m\u001b[35m/\u001b[0m\u001b[1m]\u001b[0m                                         \n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #000080; text-decoration-color: #000080\">INFO    </span> <span style=\"font-weight: bold\">[</span>timestamp<span style=\"font-weight: bold\">]</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">07</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">26</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">09:03:33</span><span style=\"font-weight: bold\">[</span><span style=\"color: #800080; text-decoration-color: #800080\">/</span><span style=\"font-weight: bold\">]</span> | <span style=\"font-weight: bold\">[</span>logger.name<span style=\"font-weight: bold\">]</span>omnidocs.tasks.ocr_extraction.extractors.paddle<span style=\"font-weight: bold\">[</span><span style=\"color: #800080; text-decoration-color: #800080\">/</span><span style=\"font-weight: bold\">]</span> |     \n", "         <span style=\"font-weight: bold\">[</span>function<span style=\"font-weight: bold\">]</span>logging.py:<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">150</span><span style=\"font-weight: bold\">[</span><span style=\"color: #800080; text-decoration-color: #800080\">/</span><span style=\"font-weight: bold\">]</span> | <span style=\"font-weight: bold\">[</span>info<span style=\"font-weight: bold\">]</span>extract completed in <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">7.</span>61s<span style=\"font-weight: bold\">[</span><span style=\"color: #800080; text-decoration-color: #800080\">/</span><span style=\"font-weight: bold\">]</span>                                         \n", "</pre>\n"], "text/plain": ["\u001b[34mINFO    \u001b[0m \u001b[1m[\u001b[0mtimestamp\u001b[1m]\u001b[0m\u001b[1;36m2025\u001b[0m-\u001b[1;36m07\u001b[0m-\u001b[1;36m26\u001b[0m \u001b[1;92m09:03:33\u001b[0m\u001b[1m[\u001b[0m\u001b[35m/\u001b[0m\u001b[1m]\u001b[0m | \u001b[1m[\u001b[0mlogger.name\u001b[1m]\u001b[0momnidocs.tasks.ocr_extraction.extractors.paddle\u001b[1m[\u001b[0m\u001b[35m/\u001b[0m\u001b[1m]\u001b[0m |     \n", "         \u001b[1m[\u001b[0mfunction\u001b[1m]\u001b[0mlogging.py:\u001b[1;36m150\u001b[0m\u001b[1m[\u001b[0m\u001b[35m/\u001b[0m\u001b[1m]\u001b[0m | \u001b[1m[\u001b[0minfo\u001b[1m]\u001b[0mextract completed in \u001b[1;36m7.\u001b[0m61s\u001b[1m[\u001b[0m\u001b[35m/\u001b[0m\u001b[1m]\u001b[0m                                         \n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["[2025-07-26 09:03:33,422] [    INFO] logging.py:150 - extract completed in 7.61s\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Text length: 1176 chars\n", "SUCCESS\n", "\n", "Testing TesseractOCRExtractor\n", "----------------------------------------\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #000080; text-decoration-color: #000080\">INFO    </span> <span style=\"font-weight: bold\">[</span>timestamp<span style=\"font-weight: bold\">]</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">07</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">26</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">09:03:37</span><span style=\"font-weight: bold\">[</span><span style=\"color: #800080; text-decoration-color: #800080\">/</span><span style=\"font-weight: bold\">]</span> | <span style=\"font-weight: bold\">[</span>logger.name<span style=\"font-weight: bold\">]</span>omnidocs.tasks.ocr_extraction.extractors.tesseract_ocr<span style=\"font-weight: bold\">[</span><span style=\"color: #800080; text-decoration-color: #800080\">/</span><span style=\"font-weight: bold\">]</span>\n", "         | <span style=\"font-weight: bold\">[</span>function<span style=\"font-weight: bold\">]</span>logging.py:<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">150</span><span style=\"font-weight: bold\">[</span><span style=\"color: #800080; text-decoration-color: #800080\">/</span><span style=\"font-weight: bold\">]</span> | <span style=\"font-weight: bold\">[</span>info<span style=\"font-weight: bold\">]</span>extract completed in <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2.</span>38s<span style=\"font-weight: bold\">[</span><span style=\"color: #800080; text-decoration-color: #800080\">/</span><span style=\"font-weight: bold\">]</span>                                       \n", "</pre>\n"], "text/plain": ["\u001b[34mINFO    \u001b[0m \u001b[1m[\u001b[0mtimestamp\u001b[1m]\u001b[0m\u001b[1;36m2025\u001b[0m-\u001b[1;36m07\u001b[0m-\u001b[1;36m26\u001b[0m \u001b[1;92m09:03:37\u001b[0m\u001b[1m[\u001b[0m\u001b[35m/\u001b[0m\u001b[1m]\u001b[0m | \u001b[1m[\u001b[0mlogger.name\u001b[1m]\u001b[0momnidocs.tasks.ocr_extraction.extractors.tesseract_ocr\u001b[1m[\u001b[0m\u001b[35m/\u001b[0m\u001b[1m]\u001b[0m\n", "         | \u001b[1m[\u001b[0mfunction\u001b[1m]\u001b[0mlogging.py:\u001b[1;36m150\u001b[0m\u001b[1m[\u001b[0m\u001b[35m/\u001b[0m\u001b[1m]\u001b[0m | \u001b[1m[\u001b[0minfo\u001b[1m]\u001b[0mextract completed in \u001b[1;36m2.\u001b[0m38s\u001b[1m[\u001b[0m\u001b[35m/\u001b[0m\u001b[1m]\u001b[0m                                       \n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #000080; text-decoration-color: #000080\">INFO    </span> <span style=\"font-weight: bold\">[</span>timestamp<span style=\"font-weight: bold\">]</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">07</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">26</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">09:03:37</span><span style=\"font-weight: bold\">[</span><span style=\"color: #800080; text-decoration-color: #800080\">/</span><span style=\"font-weight: bold\">]</span> | <span style=\"font-weight: bold\">[</span>logger.name<span style=\"font-weight: bold\">]</span>omnidocs.tasks.ocr_extraction.extractors.tesseract_ocr<span style=\"font-weight: bold\">[</span><span style=\"color: #800080; text-decoration-color: #800080\">/</span><span style=\"font-weight: bold\">]</span>\n", "         | <span style=\"font-weight: bold\">[</span>function<span style=\"font-weight: bold\">]</span>logging.py:<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">150</span><span style=\"font-weight: bold\">[</span><span style=\"color: #800080; text-decoration-color: #800080\">/</span><span style=\"font-weight: bold\">]</span> | <span style=\"font-weight: bold\">[</span>info<span style=\"font-weight: bold\">]</span>extract completed in <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2.</span>38s<span style=\"font-weight: bold\">[</span><span style=\"color: #800080; text-decoration-color: #800080\">/</span><span style=\"font-weight: bold\">]</span>                                       \n", "</pre>\n"], "text/plain": ["\u001b[34mINFO    \u001b[0m \u001b[1m[\u001b[0mtimestamp\u001b[1m]\u001b[0m\u001b[1;36m2025\u001b[0m-\u001b[1;36m07\u001b[0m-\u001b[1;36m26\u001b[0m \u001b[1;92m09:03:37\u001b[0m\u001b[1m[\u001b[0m\u001b[35m/\u001b[0m\u001b[1m]\u001b[0m | \u001b[1m[\u001b[0mlogger.name\u001b[1m]\u001b[0momnidocs.tasks.ocr_extraction.extractors.tesseract_ocr\u001b[1m[\u001b[0m\u001b[35m/\u001b[0m\u001b[1m]\u001b[0m\n", "         | \u001b[1m[\u001b[0mfunction\u001b[1m]\u001b[0mlogging.py:\u001b[1;36m150\u001b[0m\u001b[1m[\u001b[0m\u001b[35m/\u001b[0m\u001b[1m]\u001b[0m | \u001b[1m[\u001b[0minfo\u001b[1m]\u001b[0mextract completed in \u001b[1;36m2.\u001b[0m38s\u001b[1m[\u001b[0m\u001b[35m/\u001b[0m\u001b[1m]\u001b[0m                                       \n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["[2025-07-26 09:03:37,887] [    INFO] logging.py:150 - extract completed in 2.38s\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Text length: 1095 chars\n", "SUCCESS\n", "\n", "Testing EasyOCRExtractor\n", "----------------------------------------\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #000080; text-decoration-color: #000080\">INFO    </span> <span style=\"font-weight: bold\">[</span>timestamp<span style=\"font-weight: bold\">]</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">07</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">26</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">09:04:20</span><span style=\"font-weight: bold\">[</span><span style=\"color: #800080; text-decoration-color: #800080\">/</span><span style=\"font-weight: bold\">]</span> | <span style=\"font-weight: bold\">[</span>logger.name<span style=\"font-weight: bold\">]</span>omnidocs.tasks.ocr_extraction.extractors.easy_ocr<span style=\"font-weight: bold\">[</span><span style=\"color: #800080; text-decoration-color: #800080\">/</span><span style=\"font-weight: bold\">]</span> |   \n", "         <span style=\"font-weight: bold\">[</span>function<span style=\"font-weight: bold\">]</span>logging.py:<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">150</span><span style=\"font-weight: bold\">[</span><span style=\"color: #800080; text-decoration-color: #800080\">/</span><span style=\"font-weight: bold\">]</span> | <span style=\"font-weight: bold\">[</span>info<span style=\"font-weight: bold\">]</span>extract completed in <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">29.</span>11s<span style=\"font-weight: bold\">[</span><span style=\"color: #800080; text-decoration-color: #800080\">/</span><span style=\"font-weight: bold\">]</span>                                        \n", "</pre>\n"], "text/plain": ["\u001b[34mINFO    \u001b[0m \u001b[1m[\u001b[0mtimestamp\u001b[1m]\u001b[0m\u001b[1;36m2025\u001b[0m-\u001b[1;36m07\u001b[0m-\u001b[1;36m26\u001b[0m \u001b[1;92m09:04:20\u001b[0m\u001b[1m[\u001b[0m\u001b[35m/\u001b[0m\u001b[1m]\u001b[0m | \u001b[1m[\u001b[0mlogger.name\u001b[1m]\u001b[0momnidocs.tasks.ocr_extraction.extractors.easy_ocr\u001b[1m[\u001b[0m\u001b[35m/\u001b[0m\u001b[1m]\u001b[0m |   \n", "         \u001b[1m[\u001b[0mfunction\u001b[1m]\u001b[0mlogging.py:\u001b[1;36m150\u001b[0m\u001b[1m[\u001b[0m\u001b[35m/\u001b[0m\u001b[1m]\u001b[0m | \u001b[1m[\u001b[0minfo\u001b[1m]\u001b[0mextract completed in \u001b[1;36m29.\u001b[0m11s\u001b[1m[\u001b[0m\u001b[35m/\u001b[0m\u001b[1m]\u001b[0m                                        \n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #000080; text-decoration-color: #000080\">INFO    </span> <span style=\"font-weight: bold\">[</span>timestamp<span style=\"font-weight: bold\">]</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">07</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">26</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">09:04:20</span><span style=\"font-weight: bold\">[</span><span style=\"color: #800080; text-decoration-color: #800080\">/</span><span style=\"font-weight: bold\">]</span> | <span style=\"font-weight: bold\">[</span>logger.name<span style=\"font-weight: bold\">]</span>omnidocs.tasks.ocr_extraction.extractors.easy_ocr<span style=\"font-weight: bold\">[</span><span style=\"color: #800080; text-decoration-color: #800080\">/</span><span style=\"font-weight: bold\">]</span> |   \n", "         <span style=\"font-weight: bold\">[</span>function<span style=\"font-weight: bold\">]</span>logging.py:<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">150</span><span style=\"font-weight: bold\">[</span><span style=\"color: #800080; text-decoration-color: #800080\">/</span><span style=\"font-weight: bold\">]</span> | <span style=\"font-weight: bold\">[</span>info<span style=\"font-weight: bold\">]</span>extract completed in <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">29.</span>11s<span style=\"font-weight: bold\">[</span><span style=\"color: #800080; text-decoration-color: #800080\">/</span><span style=\"font-weight: bold\">]</span>                                        \n", "</pre>\n"], "text/plain": ["\u001b[34mINFO    \u001b[0m \u001b[1m[\u001b[0mtimestamp\u001b[1m]\u001b[0m\u001b[1;36m2025\u001b[0m-\u001b[1;36m07\u001b[0m-\u001b[1;36m26\u001b[0m \u001b[1;92m09:04:20\u001b[0m\u001b[1m[\u001b[0m\u001b[35m/\u001b[0m\u001b[1m]\u001b[0m | \u001b[1m[\u001b[0mlogger.name\u001b[1m]\u001b[0momnidocs.tasks.ocr_extraction.extractors.easy_ocr\u001b[1m[\u001b[0m\u001b[35m/\u001b[0m\u001b[1m]\u001b[0m |   \n", "         \u001b[1m[\u001b[0mfunction\u001b[1m]\u001b[0mlogging.py:\u001b[1;36m150\u001b[0m\u001b[1m[\u001b[0m\u001b[35m/\u001b[0m\u001b[1m]\u001b[0m | \u001b[1m[\u001b[0minfo\u001b[1m]\u001b[0mextract completed in \u001b[1;36m29.\u001b[0m11s\u001b[1m[\u001b[0m\u001b[35m/\u001b[0m\u001b[1m]\u001b[0m                                        \n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["[2025-07-26 09:04:20,406] [    INFO] logging.py:150 - extract completed in 29.11s\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Text length: 1124 chars\n", "SUCCESS\n", "\n", "Testing SuryaOCRExtractor\n", "----------------------------------------\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Detecting bboxes: 100%|██████████| 1/1 [00:13<00:00, 14.00s/it]\n", "Recognizing Text: 100%|██████████| 86/86 [06:35<00:00,  4.60s/it]  \n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #000080; text-decoration-color: #000080\">INFO    </span> <span style=\"font-weight: bold\">[</span>timestamp<span style=\"font-weight: bold\">]</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">07</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">26</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">09:11:26</span><span style=\"font-weight: bold\">[</span><span style=\"color: #800080; text-decoration-color: #800080\">/</span><span style=\"font-weight: bold\">]</span> | <span style=\"font-weight: bold\">[</span>logger.name<span style=\"font-weight: bold\">]</span>omnidocs.tasks.ocr_extraction.extractors.surya_ocr<span style=\"font-weight: bold\">[</span><span style=\"color: #800080; text-decoration-color: #800080\">/</span><span style=\"font-weight: bold\">]</span> |  \n", "         <span style=\"font-weight: bold\">[</span>function<span style=\"font-weight: bold\">]</span>logging.py:<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">150</span><span style=\"font-weight: bold\">[</span><span style=\"color: #800080; text-decoration-color: #800080\">/</span><span style=\"font-weight: bold\">]</span> | <span style=\"font-weight: bold\">[</span>info<span style=\"font-weight: bold\">]</span>extract completed in <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">410.</span>10s<span style=\"font-weight: bold\">[</span><span style=\"color: #800080; text-decoration-color: #800080\">/</span><span style=\"font-weight: bold\">]</span>                                       \n", "</pre>\n"], "text/plain": ["\u001b[34mINFO    \u001b[0m \u001b[1m[\u001b[0mtimestamp\u001b[1m]\u001b[0m\u001b[1;36m2025\u001b[0m-\u001b[1;36m07\u001b[0m-\u001b[1;36m26\u001b[0m \u001b[1;92m09:11:26\u001b[0m\u001b[1m[\u001b[0m\u001b[35m/\u001b[0m\u001b[1m]\u001b[0m | \u001b[1m[\u001b[0mlogger.name\u001b[1m]\u001b[0momnidocs.tasks.ocr_extraction.extractors.surya_ocr\u001b[1m[\u001b[0m\u001b[35m/\u001b[0m\u001b[1m]\u001b[0m |  \n", "         \u001b[1m[\u001b[0mfunction\u001b[1m]\u001b[0mlogging.py:\u001b[1;36m150\u001b[0m\u001b[1m[\u001b[0m\u001b[35m/\u001b[0m\u001b[1m]\u001b[0m | \u001b[1m[\u001b[0minfo\u001b[1m]\u001b[0mextract completed in \u001b[1;36m410.\u001b[0m10s\u001b[1m[\u001b[0m\u001b[35m/\u001b[0m\u001b[1m]\u001b[0m                                       \n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #000080; text-decoration-color: #000080\">INFO    </span> <span style=\"font-weight: bold\">[</span>timestamp<span style=\"font-weight: bold\">]</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">07</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">26</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">09:11:26</span><span style=\"font-weight: bold\">[</span><span style=\"color: #800080; text-decoration-color: #800080\">/</span><span style=\"font-weight: bold\">]</span> | <span style=\"font-weight: bold\">[</span>logger.name<span style=\"font-weight: bold\">]</span>omnidocs.tasks.ocr_extraction.extractors.surya_ocr<span style=\"font-weight: bold\">[</span><span style=\"color: #800080; text-decoration-color: #800080\">/</span><span style=\"font-weight: bold\">]</span> |  \n", "         <span style=\"font-weight: bold\">[</span>function<span style=\"font-weight: bold\">]</span>logging.py:<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">150</span><span style=\"font-weight: bold\">[</span><span style=\"color: #800080; text-decoration-color: #800080\">/</span><span style=\"font-weight: bold\">]</span> | <span style=\"font-weight: bold\">[</span>info<span style=\"font-weight: bold\">]</span>extract completed in <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">410.</span>10s<span style=\"font-weight: bold\">[</span><span style=\"color: #800080; text-decoration-color: #800080\">/</span><span style=\"font-weight: bold\">]</span>                                       \n", "</pre>\n"], "text/plain": ["\u001b[34mINFO    \u001b[0m \u001b[1m[\u001b[0mtimestamp\u001b[1m]\u001b[0m\u001b[1;36m2025\u001b[0m-\u001b[1;36m07\u001b[0m-\u001b[1;36m26\u001b[0m \u001b[1;92m09:11:26\u001b[0m\u001b[1m[\u001b[0m\u001b[35m/\u001b[0m\u001b[1m]\u001b[0m | \u001b[1m[\u001b[0mlogger.name\u001b[1m]\u001b[0momnidocs.tasks.ocr_extraction.extractors.surya_ocr\u001b[1m[\u001b[0m\u001b[35m/\u001b[0m\u001b[1m]\u001b[0m |  \n", "         \u001b[1m[\u001b[0mfunction\u001b[1m]\u001b[0mlogging.py:\u001b[1;36m150\u001b[0m\u001b[1m[\u001b[0m\u001b[35m/\u001b[0m\u001b[1m]\u001b[0m | \u001b[1m[\u001b[0minfo\u001b[1m]\u001b[0mextract completed in \u001b[1;36m410.\u001b[0m10s\u001b[1m[\u001b[0m\u001b[35m/\u001b[0m\u001b[1m]\u001b[0m                                       \n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["[2025-07-26 09:11:26,539] [    INFO] logging.py:150 - extract completed in 410.10s\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Text length: 1274 chars\n", "SUCCESS\n"]}], "source": ["test_ocr_extraction()\n"]}, {"cell_type": "code", "execution_count": null, "id": "a9ba62bd", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "final", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 5}