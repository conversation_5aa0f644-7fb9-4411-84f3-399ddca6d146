#!/usr/bin/env python3
"""
Test script for table visualization functionality.
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__)))

from omnidocs.tasks.table_extraction.extractors import (
    CamelotExtractor,
    PDFPlumberExtractor,
    TabulaExtractor
)

def test_table_visualization():
    """Test table visualization with different extractors."""
    
    # Test with PDF extractors
    pdf_extractors = [
        CamelotExtractor,
        PDFPlumberExtractor,
        TabulaExtractor
    ]
    
    pdf_path = "tests/table_extraction/assets/table_document.pdf"
    
    print("Testing table visualization...")
    
    for extractor_cls in pdf_extractors:
        print(f"\n{'='*50}")
        print(f"Testing {extractor_cls.__name__} visualization")
        print(f"{'='*50}")
        
        try:
            # Extract tables
            extractor = extractor_cls(show_log=True)
            result = extractor.extract(pdf_path)
            
            print(f"Extracted {len(result.tables)} table(s)")
            
            if result.tables:
                # Create visualization
                vis_path = f"visualized_{extractor_cls.__name__}.png"
                extractor.visualize(
                    table_result=result,
                    image_path=pdf_path,  # This will need to be converted to image
                    output_path=vis_path,
                    show_text=True,
                    show_table_ids=True
                )
                print(f"Visualization saved to: {vis_path}")
                
                # Test JSON visualization
                json_path = f"output_{extractor_cls.__name__}_pdf.json"
                if os.path.exists(json_path):
                    vis_json_path = f"visualized_from_json_{extractor_cls.__name__}.png"
                    extractor.visualize_from_json(
                        image_path=pdf_path,
                        json_path=json_path,
                        output_path=vis_json_path,
                        show_text=True
                    )
                    print(f"JSON visualization saved to: {vis_json_path}")
            else:
                print("No tables found to visualize")
                
        except Exception as e:
            print(f"ERROR: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    test_table_visualization()
