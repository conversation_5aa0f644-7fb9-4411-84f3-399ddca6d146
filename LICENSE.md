# **License**

OmniDocs incorporates multiple models, libraries, and components, each with its own licensing terms. To ensure full compliance and proper attribution, we will follow the respective licenses for each included component.

## **License Compliance**

This project adheres to the licenses associated with the libraries, models, and components it integrates. Please check below for the specific licenses used in OmniDocs:

- **Library/Component 1** - License type (e.g., MIT, GPL-3.0)
- **Library/Component 2** - License type (e.g., Apache 2.0)
- **Model 1** - License type (e.g., Creative Commons, Commercial License)
- **Model 2** - License type (e.g., MIT)

Each component’s license will be clearly noted in the respective files or directories where it is used.

## **License Questions or Missing Credits**

If any license or credit is not correctly mentioned, or if you notice that a component's attribution is missing, please do not hesitate to contact me.

You can reach me at:  
**Email:** [<EMAIL>](mailto:<EMAIL>)

I will ensure that any necessary changes are made promptly, in accordance with your query, and provide proper attribution or license updates.

## **Usage of OmniDocs**

By using this project, you acknowledge that you have reviewed and agreed to the terms and conditions of all applicable licenses.
