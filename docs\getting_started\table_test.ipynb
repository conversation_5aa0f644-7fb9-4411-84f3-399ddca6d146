from omnidocs.tasks.table_extraction.extractors import (
    CamelotExtractor,
    PDFPlumberExtractor,
    TabulaExtractor,
    TableTransformerExtractor,
    TableFormerExtractor
)

def test_table_extraction():
    from omnidocs.tasks.table_extraction.extractors import (
        CamelotExtractor,
        PDFPlumberExtractor,
        TabulaExtractor,
        TableTransformerExtractor,
        TableFormerExtractor
    )
    
    # PDF extractors
    pdf_extractors = [CamelotExtractor, PDFPlumberExtractor, TabulaExtractor]
    pdf_path = "C:\\Users\\<USER>\\OneDrive\\Desktop\\CogLab\\11-07-2025\\Omnidocs\\tests\\table_extraction\\assets\\table_document.pdf"
    
    # Image extractors  
    image_extractors = [TableTransformerExtractor, TableFormerExtractor]
    image_path = "C:\\Users\\<USER>\\OneDrive\\Desktop\\CogLab\\11-07-2025\\Omnidocs\\tests\\table_extraction\\assets\\table_image.png"
    
    # For visualization: use image file even for PDF extractors
    # (We extract from PDF but visualize on the corresponding image)
    viz_image_path = image_path
    
    print("Testing PDF table extractors...")
    print("Note: Extracting from PDF but visualizing on corresponding image")
    for extractor_cls in pdf_extractors:
        print(f"\nTesting {extractor_cls.__name__}")
        print("-" * 40)
        
        try:
            extractor = extractor_cls(show_log=True)
            result = extractor.extract(pdf_path)
            print(f"Extracted {len(result.tables)} table(s)")
            
            if result.tables:
                # Basic visualization - clean boxes only
                vis_path = f"visualized_{extractor_cls.__name__}.png"
                extractor.visualize(result, viz_image_path, vis_path)
                
                # Custom styling - clean colored boxes
                extractor.visualize(
                    result, 
                    viz_image_path, 
                    f"styled_{extractor_cls.__name__}.png",
                    table_color='red',
                    cell_color='blue', 
                    box_width=3,
                    show_text=False,  # No text overlay
                    show_table_ids=True
                )
            
            print("SUCCESS")
        except Exception as e:
            print(f"ERROR: {e}")
    
    print("\n" + "="*50)
    print("Testing Image table extractors...")
    for extractor_cls in image_extractors:
        print(f"\nTesting {extractor_cls.__name__}")
        print("-" * 40)
        
        try:
            extractor = extractor_cls(show_log=True)
            result = extractor.extract(image_path)
            print(f"Extracted {len(result.tables)} table(s)")
            
            if result.tables:
                # Basic visualization
                vis_path = f"visualized_{extractor_cls.__name__}.png"
                extractor.visualize(result, image_path, vis_path)
                
                # Custom styling
                extractor.visualize(
                    result, 
                    image_path, 
                    f"styled_{extractor_cls.__name__}.png",
                    table_color='purple',
                    cell_color='orange', 
                    box_width=2,
                    text_color='black',
                    show_table_ids=True
                )
            
            print("SUCCESS")
        except Exception as e:
            print(f"ERROR: {e}")

test_table_extraction()