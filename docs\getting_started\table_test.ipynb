{"cells": [{"cell_type": "code", "execution_count": null, "id": "8b6f615e", "metadata": {}, "outputs": [], "source": ["from omnidocs.tasks.table_extraction.extractors import (\n", "    <PERSON>lotExtractor,\n", "    PDFPlumberExtractor,\n", "    TabulaExtractor,\n", "    TableTransformerExtractor,\n", "    TableFormerExtractor\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "3c009f75", "metadata": {}, "outputs": [], "source": ["def test_table_extraction():\n", "    from omnidocs.tasks.table_extraction.extractors import (\n", "        <PERSON>lotExtractor,\n", "        PDFPlumberExtractor,\n", "        TabulaExtractor,\n", "        TableTransformerExtractor,\n", "        TableFormerExtractor\n", "    )\n", "    \n", "    # PDF extractors\n", "    pdf_extractors = [CamelotExtractor, PDFPlumberExtractor, TabulaExtractor]\n", "    pdf_path = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\CogLab\\\\11-07-2025\\\\Omnidocs\\\\tests\\\\table_extraction\\\\assets\\\\table_document.pdf\"\n", "    \n", "    # Image extractors  \n", "    image_extractors = [TableTransformerExtractor, TableFormerExtractor]\n", "    image_path = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\CogLab\\\\11-07-2025\\\\Omnidocs\\\\tests\\\\table_extraction\\\\assets\\\\table_image.png\"\n", "    \n", "    # For visualization: use image file even for PDF extractors\n", "    # (We extract from PDF but visualize on the corresponding image)\n", "    viz_image_path = image_path\n", "    \n", "    print(\"Testing PDF table extractors...\")\n", "    print(\"Note: Extracting from PDF but visualizing on corresponding image\")\n", "    for extractor_cls in pdf_extractors:\n", "        print(f\"\\nTesting {extractor_cls.__name__}\")\n", "        print(\"-\" * 40)\n", "        \n", "        try:\n", "            extractor = extractor_cls(show_log=True)\n", "            result = extractor.extract(pdf_path)\n", "            print(f\"Extracted {len(result.tables)} table(s)\")\n", "            \n", "            if result.tables:\n", "                # Basic visualization - clean boxes only\n", "                vis_path = f\"visualized_{extractor_cls.__name__}.png\"\n", "                extractor.visualize(result, viz_image_path, vis_path)\n", "                \n", "                # Custom styling - clean colored boxes\n", "                extractor.visualize(\n", "                    result, \n", "                    viz_image_path, \n", "                    f\"styled_{extractor_cls.__name__}.png\",\n", "                    table_color='red',\n", "                    cell_color='blue', \n", "                    box_width=3,\n", "                    show_text=False,  # No text overlay\n", "                    show_table_ids=True\n", "                )\n", "            \n", "            print(\"SUCCESS\")\n", "        except Exception as e:\n", "            print(f\"ERROR: {e}\")\n", "    \n", "    print(\"\\n\" + \"=\"*50)\n", "    print(\"Testing Image table extractors...\")\n", "    for extractor_cls in image_extractors:\n", "        print(f\"\\nTesting {extractor_cls.__name__}\")\n", "        print(\"-\" * 40)\n", "        \n", "        try:\n", "            extractor = extractor_cls(show_log=True)\n", "            result = extractor.extract(image_path)\n", "            print(f\"Extracted {len(result.tables)} table(s)\")\n", "            \n", "            if result.tables:\n", "                # Basic visualization\n", "                vis_path = f\"visualized_{extractor_cls.__name__}.png\"\n", "                extractor.visualize(result, image_path, vis_path)\n", "                \n", "                # Custom styling\n", "                extractor.visualize(\n", "                    result, \n", "                    image_path, \n", "                    f\"styled_{extractor_cls.__name__}.png\",\n", "                    table_color='purple',\n", "                    cell_color='orange', \n", "                    box_width=2,\n", "                    show_text=True,\n", "                    text_color='black',\n", "                    show_table_ids=True\n", "                )\n", "            \n", "            print(\"SUCCESS\")\n", "        except Exception as e:\n", "            print(f\"ERROR: {e}\")"]}, {"cell_type": "code", "execution_count": null, "id": "2a1bc4dc", "metadata": {}, "outputs": [], "source": ["test_table_extraction()"]}, {"cell_type": "code", "execution_count": null, "id": "8d0cd1e3", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "5d18f817", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "final", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 5}