import sys
import logging
from typing import Union, List, Dict, Any, Optional, Tuple
from pathlib import Path
import cv2
import numpy as np
from PIL import Image
from omnidocs.utils.logging import get_logger, log_execution_time
from omnidocs.tasks.math_expression_extraction.base import BaseLatexExtractor, BaseLatexMapper, LatexOutput

logger = get_logger(__name__)

class LaTeXOCRMapper(BaseLatexMapper):
    """Label mapper for LaTeX-OCR (pix2tex) model output."""
    
    def _setup_mapping(self):
        # Add any necessary mappings between pix2tex's LaTeX format and standard format
        mapping = {
            r"\begin{aligned}": "",  # Remove aligned environment
            r"\end{aligned}": "",
        }
        self._mapping = mapping
        self._reverse_mapping = {v: k for k, v in mapping.items()}

class LaTeXOCRExtractor(BaseLatexExtractor):
    """LaTeX-OCR (pix2tex) based expression extraction implementation."""
    
    def __init__(
        self,
        device: Optional[str] = None,
        show_log: bool = False,
        **kwargs
    ):
        """Initialize LaTeX-OCR Extractor."""
        super().__init__(device=device, show_log=show_log)
        
        self._label_mapper = LaTeXOCRMapper()
        
        try:
            from pix2tex import cli as pix2tex
        except ImportError as e:
            logger.error("Failed to import pix2tex")
            raise ImportError(
                "pix2tex is not available. Please install it with: pip install pix2tex"
            ) from e
            
        try:
            self.model = pix2tex.LatexOCR()
            if self.show_log:
                logger.success("Model initialized successfully")
        except Exception as e:
            logger.error("Failed to initialize model", exc_info=True)
            raise
    
    def _download_model(self) -> Path:
        """Model download handled by pix2tex library."""
        logger.info("Model downloading handled by pix2tex library")
        return None
    
    def _load_model(self) -> None:
        """Model loaded in __init__."""
        pass
    
    @log_execution_time
    def extract(
        self,
        input_path: Union[str, Path, Image.Image],
        **kwargs
    ) -> LatexOutput:
        """Extract LaTeX expressions using LaTeX-OCR."""
        try:
            # Preprocess input
            images = self.preprocess_input(input_path)
            
            expressions = []
            for img in images:
                # Run inference
                latex_expr = self.model(img)
                
                # Map to standard format
                mapped_expr = self.map_expression(latex_expr)
                expressions.append(mapped_expr)
            
            return LatexOutput(
                expressions=expressions,
                source_img_size=images[0].size if images else None
            )
            
        except Exception as e:
            logger.error("Error during extraction", exc_info=True)
            raise