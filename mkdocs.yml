site_name: OmniDocs
repo_url: https://github.com/adithya-s-k/OmniDocs
repo_name: adithya-s-k/OmniDocs
edit_uri: edit/main/docs/

theme:
  name: material
  logo: ./assets/logo.png
  favicon: ./assets/logo.png
  palette:
    scheme: slate
    primary: teal
    accent: light blue
  features:
    - navigation.tabs
    - navigation.sections
    - navigation.top
    - search.suggest
    - search.highlight
  custom_dir: ./assets/overrides

markdown_extensions:
  - pymdownx.highlight:
      anchor_linenums: true
  - pymdownx.inlinehilite
  - pymdownx.snippets
  - pymdownx.superfences
  - admonition
  - pymdownx.details
  - pymdownx.tabbed:
      alternate_style: true

nav:
  - Home: index.md
  - Getting Started:
    - Installation: getting_started/installation.md
    - Quick Start: getting_started/quickstart.ipynb
  - Tasks:
    - Layout Analysis:
      - Overview: tasks/layout_analysis/overview.md
      - Tutorials:
        - YOLO Layout: tasks/layout_analysis/tutorials/yolo.ipynb
        - Florence: tasks/layout_analysis/tutorials/florence.ipynb
        - PaddleOCR: tasks/layout_analysis/tutorials/paddle.ipynb
        - RT-DETR: tasks/layout_analysis/tutorials/rtdetr.ipynb
        - Surya: tasks/layout_analysis/tutorials/surya.ipynb
    - Text Extraction:
      - Overview: tasks/text_extraction/overview.md
      - Tutorials:
        - PyMuPDF: tasks/text_extraction/tutorials/pymupdf.ipynb
        - PDFPlumber: tasks/text_extraction/tutorials/pdfplumber.ipynb
        - PyPDF2: tasks/text_extraction/tutorials/pypdf2.ipynb
    - Math Expression:
      - Overview: tasks/math_extraction/overview.md
      - Tutorials:
        - LaTeX OCR: tasks/math_extraction/tutorials/latex_ocr.ipynb
        - UniMathNet: tasks/math_extraction/tutorials/unimathnet.ipynb
    - OCR:
      - Overview: tasks/ocr/overview.md
      - Tutorials:
        - PaddleOCR: tasks/ocr/tutorials/paddle.ipynb
        - Tesseract: tasks/ocr/tutorials/tesseract.ipynb
        - EasyOCR: tasks/ocr/tutorials/easyocr.ipynb
    - Table Extraction:
      - Overview: tasks/table_extraction/overview.md
      - Tutorials:
        - Camelot: tasks/table_extraction/tutorials/camelot.ipynb
        - PDFPlumber: tasks/table_extraction/tutorials/pdfplumber.ipynb
  - Workflows:
    - Overview: workflows/overview.md
    - Tutorials:
      - PDF to Markdown: workflows/pdf_to_md/implementation.ipynb
      - Invoice Extraction: workflows/invoice_parser/implementation.ipynb
  - Benchmarks:
    - Overview: benchmarks/overview.md
  - API Reference:
    - Overview: api_reference/overview.md
    # - API Docs: api_reference/modules/

  
extra:
  social:
    - icon: fontawesome/brands/github
      link: https://github.com/adithya-s-k/AI-Engineering.academy
    - icon: fontawesome/brands/twitter
      link: https://x.com/adithya_s_k

plugins:
  - search
  - mkdocs-jupyter
  - social

copyright: "&copy; 2024 OmniDocs. All rights reserved."