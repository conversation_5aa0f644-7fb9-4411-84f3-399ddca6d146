#!/usr/bin/env python3
"""
Test the coordinate fix for PDF-based table extractors.
"""

import sys
sys.path.insert(0, '../..')

from omnidocs.tasks.table_extraction.extractors import (
    CamelotExtractor,
    TableTransformerExtractor
)
from PIL import Image

def test_coordinate_fix():
    """Test that PDF extractors now have correct coordinates matching image extractors."""
    
    pdf_path = "assets/table_document.pdf"
    image_path = "assets/table_image.png"
    
    print("=" * 60)
    print("TESTING COORDINATE FIX")
    print("=" * 60)
    
    # Get image size for reference
    img = Image.open(image_path)
    img_width, img_height = img.size
    print(f"Image size: {img_width} x {img_height}")
    
    print("\n1. Testing TableTransformer (CORRECT baseline)...")
    try:
        extractor = TableTransformerExtractor(show_log=False)
        result = extractor.extract(image_path)
        
        if result.tables:
            table = result.tables[0]
            print(f"   ✓ TableTransformer bbox: {table.bbox}")
            
            # Check if bbox is within image bounds
            x1, y1, x2, y2 = table.bbox
            if 0 <= x1 < x2 <= img_width and 0 <= y1 < y2 <= img_height:
                print(f"   ✓ Coordinates are within image bounds")
                tt_bbox = table.bbox
            else:
                print(f"   ✗ Coordinates are outside image bounds!")
                tt_bbox = None
        else:
            print("   ✗ No tables found")
            tt_bbox = None
            
    except Exception as e:
        print(f"   ✗ Error: {e}")
        tt_bbox = None
    
    print("\n2. Testing CamelotExtractor (FIXED)...")
    try:
        extractor = CamelotExtractor(show_log=False)
        result = extractor.extract(pdf_path)
        
        if result.tables:
            table = result.tables[0]
            print(f"   ✓ Camelot bbox: {table.bbox}")
            print(f"   ✓ Source image size: {result.source_img_size}")
            
            # Check if bbox is within image bounds
            x1, y1, x2, y2 = table.bbox
            if 0 <= x1 < x2 <= img_width and 0 <= y1 < y2 <= img_height:
                print(f"   ✓ Coordinates are within image bounds")
                camelot_bbox = table.bbox
            else:
                print(f"   ✗ Coordinates are outside image bounds!")
                print(f"     Expected: 0 <= x1({x1}) < x2({x2}) <= {img_width}")
                print(f"     Expected: 0 <= y1({y1}) < y2({y2}) <= {img_height}")
                camelot_bbox = None
        else:
            print("   ✗ No tables found")
            camelot_bbox = None
            
    except Exception as e:
        print(f"   ✗ Error: {e}")
        camelot_bbox = None
    
    print("\n3. Comparison...")
    if tt_bbox and camelot_bbox:
        # Calculate overlap/similarity
        tt_x1, tt_y1, tt_x2, tt_y2 = tt_bbox
        cam_x1, cam_y1, cam_x2, cam_y2 = camelot_bbox
        
        # Calculate IoU (Intersection over Union)
        inter_x1 = max(tt_x1, cam_x1)
        inter_y1 = max(tt_y1, cam_y1)
        inter_x2 = min(tt_x2, cam_x2)
        inter_y2 = min(tt_y2, cam_y2)
        
        if inter_x1 < inter_x2 and inter_y1 < inter_y2:
            inter_area = (inter_x2 - inter_x1) * (inter_y2 - inter_y1)
            tt_area = (tt_x2 - tt_x1) * (tt_y2 - tt_y1)
            cam_area = (cam_x2 - cam_x1) * (cam_y2 - cam_y1)
            union_area = tt_area + cam_area - inter_area
            iou = inter_area / union_area if union_area > 0 else 0
            
            print(f"   IoU (overlap): {iou:.3f}")
            if iou > 0.5:
                print("   ✓ Good overlap - coordinates are reasonably aligned!")
            else:
                print("   ⚠ Low overlap - coordinates may still need adjustment")
        else:
            print("   ✗ No overlap - coordinates are completely misaligned")
    
    print("\n4. Creating test visualizations...")
    try:
        if camelot_bbox:
            extractor = CamelotExtractor(show_log=False)
            result = extractor.extract(pdf_path)
            extractor.visualize(
                result, 
                image_path, 
                'test_coordinate_fix_camelot.png',
                show_text=False,
                show_table_ids=True
            )
            print("   ✓ Camelot visualization saved: test_coordinate_fix_camelot.png")
        
        if tt_bbox:
            extractor = TableTransformerExtractor(show_log=False)
            result = extractor.extract(image_path)
            extractor.visualize(
                result, 
                image_path, 
                'test_coordinate_fix_tabletransformer.png',
                show_text=False,
                show_table_ids=True
            )
            print("   ✓ TableTransformer visualization saved: test_coordinate_fix_tabletransformer.png")
            
    except Exception as e:
        print(f"   ✗ Visualization error: {e}")
    
    print("\n" + "=" * 60)
    print("COORDINATE FIX TEST COMPLETE")
    print("=" * 60)

if __name__ == "__main__":
    test_coordinate_fix()
