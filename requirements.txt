# ✅ WORKING DEPENDENCIES (Tested and Verified)
# Python: >=3.10,<3.13
# Installation: uv pip install -r requirements.txt

# ==========================================
# CORE ML/AI
# ==========================================
torch==2.7.1
torchvision==0.22.1
transformers==4.53.2
numpy==1.26.4  # CRITICAL: NumPy 2.0+ breaks PaddleOCR/imgaug compatibility
setuptools==80.9.0  # Required by PaddleOCR
timm==1.0.17

# ==========================================
# OCR MODELS 
# ==========================================
easyocr==1.7.2
pytesseract==0.3.13
surya-ocr==0.14.6 
protobuf==3.20.2

# PaddleOCR (working versions)
paddleocr==2.8.1
paddlepaddle==2.6.1

# ==========================================
# MATH EXPRESSION MODELS (working - 4/5 extractors)
# ==========================================
# texteller==1.0.2  not working :(
#  unimernet, nougat, donut  all working with transformers

# UniMERNet dependencies
omegaconf
iopath
albumentations
webdataset
ftfy


# Donut dependencies
sentencepiece

# Texify dependencies - not working cuz of some issues
# texify

# ==========================================
# TEXT EXTRACTION MODELS 
# ==========================================
PyMuPDF==1.26.3
PyPDF2==3.0.1
pdftext==0.6.3
docling==2.41.0
pdfplumber==0.11.7
pdf2image==1.17.0
# ==========================================
# TABLE EXTRACTION MODELS 
# ==========================================
camelot-py==1.0.0
tabula-py==2.10.0
# pdfplumber==0.11.7  # Already listed above
# Note: PPStructure, TableTransformer, TableFormer also working with transformers

# ==========================================
# OTHER REQUIRED MODULES
# ==========================================
pydantic
Pillow
rich
opencv-python
huggingface-hub
hf_xet
