#!/usr/bin/env python3
"""
Quick test for table visualization fix.
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__)))

from omnidocs.tasks.table_extraction.extractors import CamelotExtractor

def test_pdf_visualization():
    """Test PDF visualization with Camelot."""
    
    pdf_path = "tests/table_extraction/assets/table_document.pdf"
    
    print("Testing PDF visualization with CamelotExtractor...")
    
    try:
        # Extract tables
        extractor = CamelotExtractor(show_log=True)
        result = extractor.extract(pdf_path)
        
        print(f"Extracted {len(result.tables)} table(s)")
        
        if result.tables:
            # Test visualization
            vis_path = "test_camelot_viz.png"
            extractor.visualize(
                table_result=result,
                image_path=pdf_path,  # This should now work with PDF
                output_path=vis_path,
                show_text=True,
                show_table_ids=True
            )
            print(f"SUCCESS: Visualization saved to {vis_path}")
        else:
            print("No tables found to visualize")
            
    except Exception as e:
        print(f"ERROR: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_pdf_visualization()
