Stack trace:
Frame         Function      Args
0007FFFFAAA0  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFF99A0) msys-2.0.dll+0x2118E
0007FFFFAAA0  0002100469BA (000000000000, 000000000000, 000000000000, 000000000004) msys-2.0.dll+0x69BA
0007FFFFAAA0  0002100469F2 (00021028DF99, 0007FFFFA958, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFFAAA0  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFFAAA0  00021006A545 (0007FFFFAAB0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0001004F94B7  00021006B9A5 (0007FFFFAAB0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF960560000 ntdll.dll
7FF95EAB0000 KERNEL32.DLL
7FF95D860000 KERNELBASE.dll
7FF960250000 USER32.dll
7FF95D6B0000 win32u.dll
7FF960220000 GDI32.dll
7FF95DC50000 gdi32full.dll
7FF95DD90000 msvcp_win.dll
7FF95DEE0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FF95F0C0000 advapi32.dll
7FF960070000 msvcrt.dll
7FF95F210000 sechost.dll
7FF95EB80000 RPCRT4.dll
7FF95CC90000 CRYPTBASE.DLL
7FF95DE40000 bcryptPrimitives.dll
7FF95ECA0000 IMM32.DLL
