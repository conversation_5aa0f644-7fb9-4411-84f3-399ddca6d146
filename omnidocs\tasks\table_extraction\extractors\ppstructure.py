import time
import numpy as np
from typing import Union, List, Dict, Any, Optional, Tuple
from pathlib import Path
from PIL import Image
import cv2

from omnidocs.utils.logging import get_logger, log_execution_time
from omnidocs.tasks.table_extraction.base import BaseTableExtractor, BaseTableMapper, TableOutput, Table, TableCell

logger = get_logger(__name__)

class PPStructureMapper(BaseTableMapper):
    """Label mapper for PaddleOCR PPStructure table extraction output."""
    
    def __init__(self):
        super().__init__('ppstructure')
        self._setup_mapping()
    
    def _setup_mapping(self):
        """Setup language and model mappings for PPStructure."""
        self._language_mapping = {
            'en': 'en',
            'ch': 'ch',
            'chinese_cht': 'chinese_cht',
            'fr': 'fr',
            'german': 'german',
            'japan': 'japan',
            'korean': 'korean',
        }
        
        self._layout_models = {
            'en': 'picodet_lcnet_x1_0_fgd_layout_cdla_infer',
            'ch': 'picodet_lcnet_x1_0_fgd_layout_infer'
        }
        
        self._table_models = {
            'en': 'SLANet',
            'ch': 'SLANet_ch'
        }

class PPStructureExtractor(BaseTableExtractor):
    """PaddleOCR PPStructure based table extraction implementation."""
    
    def __init__(
        self,
        device: Optional[str] = None,
        show_log: bool = False,
        languages: Optional[List[str]] = None,
        use_gpu: bool = True,
        layout_model: Optional[str] = None,
        table_model: Optional[str] = None,
        return_ocr_result_in_table: bool = True,
        **kwargs
    ):
        """Initialize PPStructure Table Extractor."""
        super().__init__(
            device=device,
            show_log=show_log,
            engine_name='ppstructure'
        )
        
        self._label_mapper = PPStructureMapper()
        self.languages = languages or ['en']
        self.use_gpu = use_gpu
        self.layout_model = layout_model
        self.table_model = table_model
        self.return_ocr_result_in_table = return_ocr_result_in_table
        
        try:
            from paddleocr import PPStructure
            self.PPStructure = PPStructure
            
        except ImportError as e:
            logger.error("Failed to import PPStructure")
            raise ImportError(
                "PPStructure is not available. Please install it with: pip install paddlepaddle paddleocr"
            ) from e
        
        self._load_model()
    
    def _download_model(self) -> Optional[Path]:
        """
        Model download is handled automatically by PPStructure library.
        This method is required by the abstract base class.
        """
        if self.show_log:
            logger.info("Model downloading is handled automatically by PPStructure library")
        return None
    
    def _load_model(self) -> None:
        """Load PPStructure models."""
        try:
            # Get the primary language
            primary_lang = self.languages[0] if self.languages else 'en'
            mapped_lang = self._label_mapper._language_mapping.get(primary_lang, 'en')
            
            # Set default models if not specified
            if not self.layout_model:
                self.layout_model = self._label_mapper._layout_models.get(mapped_lang, 
                    self._label_mapper._layout_models['en'])
            
            if not self.table_model:
                self.table_model = self._label_mapper._table_models.get(mapped_lang,
                    self._label_mapper._table_models['en'])
            
            # Initialize PPStructure
            self.pp_structure = self.PPStructure(
                use_gpu=self.use_gpu,
                show_log=self.show_log,
                lang=mapped_lang,
                layout_model_dir=self.layout_model,
                table_model_dir=self.table_model,
                return_ocr_result_in_table=self.return_ocr_result_in_table
            )
            
            if self.show_log:
                logger.info(f"PPStructure models loaded with language: {mapped_lang}")
                
        except Exception as e:
            logger.error("Failed to load PPStructure models", exc_info=True)
            raise
    
    def preprocess_input(self, input_path: Union[str, Path, Image.Image]) -> List[Image.Image]:
        """
        Convert input to processable format.
        PPStructure can handle both images and PDFs.

        Args:
            input_path: Path to input image or PDF, or PIL Image

        Returns:
            List of PIL Images
        """
        if isinstance(input_path, Image.Image):
            return [input_path.convert('RGB')]

        input_path = Path(input_path)
        if not input_path.exists():
            raise FileNotFoundError(f"Input file not found: {input_path}")

        if input_path.suffix.lower() == '.pdf':
            # Convert PDF to images using PyMuPDF (fitz)
            try:
                import fitz  # PyMuPDF
                images = []
                pdf_doc = fitz.open(str(input_path))

                for page_num in range(pdf_doc.page_count):
                    page = pdf_doc[page_num]
                    # Render page to image with 2x scaling for better quality
                    mat = fitz.Matrix(2.0, 2.0)
                    pix = page.get_pixmap(matrix=mat)
                    img_data = pix.tobytes("ppm")

                    # Convert to PIL Image
                    from io import BytesIO
                    img = Image.open(BytesIO(img_data)).convert('RGB')
                    images.append(img)

                pdf_doc.close()
                return images

            except ImportError:
                logger.error("PyMuPDF not available. Install with: pip install PyMuPDF")
                raise ImportError("PyMuPDF is required for PDF processing. Install with: pip install PyMuPDF")
        else:
            # Load single image
            image = Image.open(input_path).convert('RGB')
            return [image]

    def postprocess_output(self, raw_output: List[Dict], img_size: Tuple[int, int]) -> TableOutput:
        """Convert PPStructure output to standardized TableOutput format."""
        tables = []
        
        # Debug: print raw_output to see the actual structure
        if self.show_log:
            logger.info(f"Raw PPStructure output: {raw_output}")
            logger.info(f"Number of results: {len(raw_output)}")
            for i, result in enumerate(raw_output):
                logger.info(f"Result {i}: type={result.get('type')}, keys={list(result.keys())}")
                if 'res' in result:
                    logger.info(f"Result {i} res keys: {list(result['res'].keys())}")
        
        for i, result in enumerate(raw_output):
            # Check if this is a table result
            if result.get('type') != 'table':
                if self.show_log:
                    logger.info(f"Skipping result {i}: not a table (type={result.get('type')})")
                continue
            
            # Get table result data
            table_res = result.get('res', {})
            if not table_res:
                if self.show_log:
                    logger.warning(f"Result {i}: empty res field")
                continue
            
            cells = []
            
            # Try different parsing strategies based on available data
            if 'html' in table_res:
                # Strategy 1: Parse HTML table (most common format)
                if self.show_log:
                    logger.info(f"Result {i}: parsing HTML format")
                html_content = table_res['html']
                cells = self._parse_html_table(html_content, result.get('bbox', [0, 0, img_size[0], img_size[1]]))
                
            elif 'structure_str_list' in table_res and 'bbox_list' in table_res:
                # Strategy 2: Structure-based format
                if self.show_log:
                    logger.info(f"Result {i}: parsing structure list format")
                structure_str_list = table_res['structure_str_list']
                bbox_list = table_res['bbox_list']
                cells = self._parse_structure_list(structure_str_list, bbox_list, img_size)
                
            elif 'cells' in table_res:
                # Strategy 3: Direct cells format
                if self.show_log:
                    logger.info(f"Result {i}: parsing direct cells format")
                cells = self._parse_direct_cells(table_res['cells'], img_size)
                
            elif self.return_ocr_result_in_table and 'bbox' in table_res and 'text' in table_res:
                # Strategy 4: Simple bbox + text format
                if self.show_log:
                    logger.info(f"Result {i}: parsing bbox+text format")
                cells = self._parse_bbox_text_format(table_res, img_size)
                
            elif self.return_ocr_result_in_table and isinstance(table_res, list):
                # Strategy 5: List of OCR results
                if self.show_log:
                    logger.info(f"Result {i}: parsing OCR result list format")
                cells = self._parse_ocr_result_list(table_res, img_size)
                
            else:
                # Strategy 6: Try to extract any recognizable data
                if self.show_log:
                    logger.warning(f"Result {i}: unknown format, attempting fallback parsing")
                    logger.warning(f"Available keys: {list(table_res.keys())}")
                cells = self._parse_fallback_format(table_res, img_size)
            
            if not cells:
                if self.show_log:
                    logger.warning(f"Result {i}: no cells extracted")
                continue
            
            # Calculate table dimensions
            if cells:
                num_rows = max((cell.row + cell.rowspan for cell in cells), default=1)
                num_cols = max((cell.col + cell.colspan for cell in cells), default=1)
            else:
                num_rows = num_cols = 1
            
            # Get table bounding box
            table_bbox = result.get('bbox', [0, 0, img_size[0], img_size[1]])
            if table_bbox and len(table_bbox) >= 4:
                table_bbox = self._label_mapper.normalize_bbox(table_bbox, img_size[0], img_size[1])
            else:
                table_bbox = [0.0, 0.0, 1.0, 1.0]
            
            # Create table object
            table = Table(
                cells=cells,
                num_rows=num_rows,
                num_cols=num_cols,
                bbox=table_bbox,
                confidence=result.get('confidence', 0.9),
                table_id=f"table_{i}",
                structure_confidence=result.get('confidence', 0.9)
            )
            
            tables.append(table)
            
            if self.show_log:
                logger.info(f"Successfully created table {i} with {len(cells)} cells, {num_rows} rows, {num_cols} cols")
        
        return TableOutput(
            tables=tables,
            source_img_size=img_size,
            metadata={
                'engine': 'ppstructure',
                'language': self.languages[0] if self.languages else 'en',
                'layout_model': self.layout_model,
                'table_model': self.table_model,
                'total_results': len(raw_output),
                'table_results': len(tables)
            }
        )

    def _parse_html_table(self, html_content: str, table_bbox: List) -> List[TableCell]:
        """Parse HTML table content to extract cells."""
        try:
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(html_content, 'html.parser')
            table = soup.find('table')
            if not table:
                if self.show_log:
                    logger.warning("No table element found in HTML content")
                return []
            
            cells = []
            rows = table.find_all('tr')
            
            if self.show_log:
                logger.info(f"Found {len(rows)} rows in HTML table")
            
            for row_idx, row in enumerate(rows):
                col_idx = 0
                table_cells = row.find_all(['td', 'th'])
                
                for cell in table_cells:
                    text = cell.get_text(strip=True)
                    rowspan = int(cell.get('rowspan', 1))
                    colspan = int(cell.get('colspan', 1))
                    
                    # Create a simple bbox estimation based on table bbox
                    if len(table_bbox) >= 4:
                        cols_in_row = len(table_cells) if table_cells else 1
                        cell_width = (table_bbox[2] - table_bbox[0]) / max(cols_in_row, 1)
                        cell_height = (table_bbox[3] - table_bbox[1]) / max(len(rows), 1)
                        
                        cell_bbox = [
                            table_bbox[0] + (col_idx * cell_width),
                            table_bbox[1] + (row_idx * cell_height),
                            table_bbox[0] + ((col_idx + colspan) * cell_width),
                            table_bbox[1] + ((row_idx + rowspan) * cell_height)
                        ]
                    else:
                        cell_bbox = [0.0, 0.0, 1.0, 1.0]
                    
                    # Normalize bbox
                    cell_bbox = self._label_mapper.normalize_bbox(cell_bbox, 1.0, 1.0)
                    
                    table_cell = TableCell(
                        text=text,
                        row=row_idx,
                        col=col_idx,
                        rowspan=rowspan,
                        colspan=colspan,
                        bbox=cell_bbox,
                        confidence=0.9,
                        is_header=(cell.name == 'th' or row_idx == 0)
                    )
                    cells.append(table_cell)
                    col_idx += colspan
            
            if self.show_log:
                logger.info(f"Extracted {len(cells)} cells from HTML table")
            
            return cells
        except Exception as e:
            logger.error(f"Error parsing HTML table: {e}")
            return []

    def _parse_structure_list(self, structure_str_list: List, bbox_list: List, img_size: Tuple[int, int]) -> List[TableCell]:
        """Parse structure string list format."""
        cells = []
        
        if self.show_log:
            logger.info(f"Parsing structure list: {len(structure_str_list)} structures, {len(bbox_list)} bboxes")
        
        for j, (structure, bbox) in enumerate(zip(structure_str_list, bbox_list)):
            # Handle different structure formats
            if isinstance(structure, dict):
                text = structure.get('text', '').strip()
                row = structure.get('row', j // 10)  # Fallback estimation
                col = structure.get('col', j % 10)   # Fallback estimation
                rowspan = structure.get('rowspan', 1)
                colspan = structure.get('colspan', 1)
                confidence = structure.get('confidence', 0.9)
            else:
                # If structure is a string, try to parse it
                text = str(structure).strip()
                row = j // 10  # Simple fallback
                col = j % 10
                rowspan = 1
                colspan = 1
                confidence = 0.9
            
            # Normalize bbox
            if bbox and len(bbox) >= 4:
                normalized_bbox = self._label_mapper.normalize_bbox(bbox, img_size[0], img_size[1])
            else:
                # Create a default bbox
                normalized_bbox = [0.0, 0.0, 1.0, 1.0]
            
            cell = TableCell(
                text=text,
                row=row,
                col=col,
                rowspan=rowspan,
                colspan=colspan,
                bbox=normalized_bbox,
                confidence=confidence,
                is_header=(row == 0)
            )
            cells.append(cell)
        
        return cells

    def _parse_direct_cells(self, cells_data: List, img_size: Tuple[int, int]) -> List[TableCell]:
        """Parse direct cells format."""
        cells = []
        
        if self.show_log:
            logger.info(f"Parsing direct cells: {len(cells_data)} cells")
        
        for cell_data in cells_data:
            text = cell_data.get('text', '').strip()
            row = cell_data.get('row', 0)
            col = cell_data.get('col', 0)
            rowspan = cell_data.get('rowspan', 1)
            colspan = cell_data.get('colspan', 1)
            bbox = cell_data.get('bbox', [0, 0, img_size[0], img_size[1]])
            
            if bbox and len(bbox) >= 4:
                normalized_bbox = self._label_mapper.normalize_bbox(bbox, img_size[0], img_size[1])
            else:
                normalized_bbox = [0.0, 0.0, 1.0, 1.0]
            
            cell = TableCell(
                text=text,
                row=row,
                col=col,
                rowspan=rowspan,
                colspan=colspan,
                bbox=normalized_bbox,
                confidence=cell_data.get('confidence', 0.9),
                is_header=(row == 0)
            )
            cells.append(cell)
        
        return cells

    def _parse_bbox_text_format(self, table_res: Dict, img_size: Tuple[int, int]) -> List[TableCell]:
        """Parse simple bbox + text format."""
        cells = []
        
        bbox = table_res.get('bbox', [0, 0, img_size[0], img_size[1]])
        text = table_res.get('text', '').strip()
        
        if text:
            normalized_bbox = self._label_mapper.normalize_bbox(bbox, img_size[0], img_size[1])
            
            cell = TableCell(
                text=text,
                row=0,
                col=0,
                rowspan=1,
                colspan=1,
                bbox=normalized_bbox,
                confidence=table_res.get('confidence', 0.9),
                is_header=True
            )
            cells.append(cell)
        
        return cells

    def _parse_ocr_result_list(self, table_res: List, img_size: Tuple[int, int]) -> List[TableCell]:
        """Parse list of OCR results format."""
        cells = []
        
        if self.show_log:
            logger.info(f"Parsing OCR result list: {len(table_res)} items")
        
        for i, ocr_item in enumerate(table_res):
            if isinstance(ocr_item, list) and len(ocr_item) >= 2:
                bbox = ocr_item[0] if len(ocr_item[0]) >= 4 else [0, 0, img_size[0], img_size[1]]
                text = ocr_item[1][0] if isinstance(ocr_item[1], list) else str(ocr_item[1])
                confidence = ocr_item[1][1] if isinstance(ocr_item[1], list) and len(ocr_item[1]) > 1 else 0.9
            elif isinstance(ocr_item, dict):
                bbox = ocr_item.get('bbox', [0, 0, img_size[0], img_size[1]])
                text = ocr_item.get('text', '').strip()
                confidence = ocr_item.get('confidence', 0.9)
            else:
                continue
            
            # Simple grid positioning (fallback)
            row = i // 5  # Assume max 5 cols
            col = i % 5
            
            normalized_bbox = self._label_mapper.normalize_bbox(bbox, img_size[0], img_size[1])
            
            cell = TableCell(
                text=text,
                row=row,
                col=col,
                rowspan=1,
                colspan=1,
                bbox=normalized_bbox,
                confidence=confidence,
                is_header=(row == 0)
            )
            cells.append(cell)
        
        return cells

    def _parse_fallback_format(self, table_res: Dict, img_size: Tuple[int, int]) -> List[TableCell]:
        """Fallback parser for unknown formats."""
        cells = []
        
        # Try to extract any text/bbox information
        if isinstance(table_res, dict):
            # Look for common field names
            text_fields = ['text', 'content', 'value', 'html']
            bbox_fields = ['bbox', 'box', 'coordinates']
            
            text = None
            bbox = None
            
            for field in text_fields:
                if field in table_res:
                    text = str(table_res[field]).strip()
                    break
            
            for field in bbox_fields:
                if field in table_res and isinstance(table_res[field], list) and len(table_res[field]) >= 4:
                    bbox = table_res[field]
                    break
            
            if text:
                if not bbox:
                    bbox = [0, 0, img_size[0], img_size[1]]
                
                normalized_bbox = self._label_mapper.normalize_bbox(bbox, img_size[0], img_size[1])
                
                cell = TableCell(
                    text=text,
                    row=0,
                    col=0,
                    rowspan=1,
                    colspan=1,
                    bbox=normalized_bbox,
                    confidence=0.8,  # Lower confidence for fallback
                    is_header=True
                )
                cells.append(cell)
        
        return cells

    def _parse_bbox_text_format(self, table_res: Dict, img_size: Tuple[int, int]) -> List[TableCell]:
        """Parse simple bbox + text format."""
        cells = []
        
        bbox = table_res.get('bbox', [0, 0, img_size[0], img_size[1]])
        text = table_res.get('text', '').strip()
        
        if text:
            normalized_bbox = self._label_mapper.normalize_bbox(bbox, img_size[0], img_size[1])
            
            cell = TableCell(
                text=text,
                row=0,
                col=0,
                rowspan=1,
                colspan=1,
                bbox=normalized_bbox,
                confidence=table_res.get('confidence', 0.9),
                is_header=True
            )
            cells.append(cell)
        
        return cells

    def _parse_ocr_result_list(self, table_res: List, img_size: Tuple[int, int]) -> List[TableCell]:
        """Parse list of OCR results format."""
        cells = []
        
        if self.show_log:
            logger.info(f"Parsing OCR result list: {len(table_res)} items")
        
        for i, ocr_item in enumerate(table_res):
            if isinstance(ocr_item, list) and len(ocr_item) >= 2:
                bbox = ocr_item[0] if len(ocr_item[0]) >= 4 else [0, 0, img_size[0], img_size[1]]
                text = ocr_item[1][0] if isinstance(ocr_item[1], list) else str(ocr_item[1])
                confidence = ocr_item[1][1] if isinstance(ocr_item[1], list) and len(ocr_item[1]) > 1 else 0.9
            elif isinstance(ocr_item, dict):
                bbox = ocr_item.get('bbox', [0, 0, img_size[0], img_size[1]])
                text = ocr_item.get('text', '').strip()
                confidence = ocr_item.get('confidence', 0.9)
            else:
                continue
            
            # Simple grid positioning (fallback)
            row = i // 5  # Assume max 5 cols
            col = i % 5
            
            normalized_bbox = self._label_mapper.normalize_bbox(bbox, img_size[0], img_size[1])
            
            cell = TableCell(
                text=text,
                row=row,
                col=col,
                rowspan=1,
                colspan=1,
                bbox=normalized_bbox,
                confidence=confidence,
                is_header=(row == 0)
            )
            cells.append(cell)
        
        return cells

    def _parse_fallback_format(self, table_res: Dict, img_size: Tuple[int, int]) -> List[TableCell]:
        """Fallback parser for unknown formats."""
        cells = []
        
        # Try to extract any text/bbox information
        if isinstance(table_res, dict):
            # Look for common field names
            text_fields = ['text', 'content', 'value', 'html']
            bbox_fields = ['bbox', 'box', 'coordinates']
            
            text = None
            bbox = None
            
            for field in text_fields:
                if field in table_res:
                    text = str(table_res[field]).strip()
                    break
            
            for field in bbox_fields:
                if field in table_res and isinstance(table_res[field], list) and len(table_res[field]) >= 4:
                    bbox = table_res[field]
                    break
            
            if text:
                if not bbox:
                    bbox = [0, 0, img_size[0], img_size[1]]
                
                normalized_bbox = self._label_mapper.normalize_bbox(bbox, img_size[0], img_size[1])
                
                cell = TableCell(
                    text=text,
                    row=0,
                    col=0,
                    rowspan=1,
                    colspan=1,
                    bbox=normalized_bbox,
                    confidence=0.8,  # Lower confidence for fallback
                    is_header=True
                )
                cells.append(cell)
        
        return cells
    
    @log_execution_time
    def extract(
        self,
        input_path: Union[str, Path, Image.Image],
        **kwargs
    ) -> TableOutput:
        """Extract tables using PPStructure."""
        try:
            # Preprocess input
            images = self.preprocess_input(input_path)

            all_tables = []
            total_img_size = None

            # Process each page/image
            for page_idx, img in enumerate(images):
                # Convert PIL to cv2 format
                if isinstance(img, Image.Image):
                    img_cv2 = cv2.cvtColor(np.array(img), cv2.COLOR_RGB2BGR)
                else:
                    img_cv2 = img

                # Get image size
                img_size = img_cv2.shape[:2][::-1]  # (width, height)
                if total_img_size is None:
                    total_img_size = img_size

                # Perform structure analysis
                result = self.pp_structure(img_cv2)

                # Convert to standardized format
                page_output = self.postprocess_output(result, img_size)

                # Add page information to tables
                for table in page_output.tables:
                    table.metadata = table.metadata or {}
                    table.metadata['page_number'] = page_idx + 1
                    all_tables.append(table)

            # Create final output
            table_output = TableOutput(
                tables=all_tables,
                source_img_size=total_img_size,
                processing_time=None,
                metadata={"total_pages": len(images)}
            )

            if self.show_log:
                logger.info(f"Extracted {len(table_output.tables)} tables using PPStructure from {len(images)} page(s)")

            return table_output

        except Exception as e:
            logger.error("Error during PPStructure extraction", exc_info=True)
            return TableOutput(
                tables=[],
                source_img_size=None,
                processing_time=None,
                metadata={"error": str(e)}
            )
    
    def predict(self, img, **kwargs):
        """Predict method for compatibility with original interface."""
        try:
            result = self.extract(img, **kwargs)
            
            # Convert to original format
            table_res = []
            for table in result.tables:
                table_data = {
                    "table_id": table.table_id,
                    "bbox": table.bbox,
                    "confidence": table.confidence,
                    "cells": [cell.to_dict() for cell in table.cells],
                    "num_rows": table.num_rows,
                    "num_cols": table.num_cols,
                    "type": "table"
                }
                table_res.append(table_data)
            
            return table_res
            
        except Exception as e:
            logger.error("Error during PPStructure prediction", exc_info=True)
            return []